import discord
from discord.ext import commands
from discord import app_commands
import logging
import os
from dotenv import load_dotenv
import re
import asyncio
import traceback
import time
import aiohttp
import ssl
import json
import datetime
from typing import Dict, Optional, Any, List
# psutil import removed - was only used by deleted /metrics command

import sys
import os

# Import from quickbitesbot common folder
from quickbitesbot.common.bot import (
    track,
    extract_group_link
)

from quickbitesbot.common.bot import (
    process_cart_items,
    calculate_fees,
    create_locked_order_embed,

    fetch_order_details,
    track_order_status,
    OrderStatusButtons
)
from quickbitesbot.common.check_group_order import process_group_order

# Import PriceCheckerV2 for advanced pricing analysis
from .pricecheckerv2 import quickbites_bot_price_analysis

# Import our custom embed templates
from quickbitesbot.embed_templates import (
    create_error_embed,
    create_processing_embed,
    create_order_summary_embed
)

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('quickeatsbot.log', encoding='utf-8')
    ]
)

# Create a logger for this module
logger = logging.getLogger('quickeatsbot')

# Get environment variables
DISCORD_BOT_TOKEN = os.getenv("DISCORD_BOT_TOKEN")
DISCORD_GUILD_ID = int(os.getenv("DISCORD_GUILD_ID"))
TOKEN_2 = os.getenv("TOKEN_2")  # Add TOKEN_2 for channel operations
COMMANDS_GLOBAL = False

# Set up bot
intents = discord.Intents.default()
intents.messages = True
intents.guilds = True
intents.message_content = True
intents.dm_messages = True

# Create the bot with optimized settings (no command prefix - slash commands only)
bot = commands.Bot(
    command_prefix=None,  # Disable prefix commands completely
    intents=intents,
    dm_permission=True,
    case_insensitive=True,  # Make commands case-insensitive
    max_messages=10000,     # Increase message cache for better performance
    heartbeat_timeout=150.0, # Increase heartbeat timeout for stability
    help_command=None       # Disable the default help command
)

GUILD = discord.Object(id=DISCORD_GUILD_ID)

# Global session for HTTP requests
_http_session: Optional[aiohttp.ClientSession] = None

# Performance metrics removed - /metrics command deleted

# Cache for frequently accessed data
data_cache: Dict[str, Any] = {}

# Path to the tracking data file
TRACKING_DATA_FILE = 'tracking_data.json'



# Path to the status embed data file
STATUS_EMBED_DATA_FILE = 'status_embed_data.json'



# Active tracking information
active_tracking: Dict[str, Dict[str, Any]] = {}



# Status embed data storage
status_embed_data: Dict[str, Any] = {}







# Status message ID to update
STATUS_MESSAGE_ID = 1365395438542262345

def create_simplified_order_summary_embed(result, cart_items, calculated_subtotal):
    """Create a simplified order summary embed without pricing analysis."""
    embed = discord.Embed(
        title="🍔 QuickEats Order Summary (BETA)",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Extract basic information from result - handle the actual data structure
    restaurant_name = result.get('restaurant_name', 'Unknown Restaurant')
    restaurant_url = result.get('store_url', '')  # Use store_url from process_group_order

    # Build delivery address from location data
    location = result.get('location', {})
    if location and location.get('address'):
        address_parts = []
        if location.get('address'):
            address_parts.append(location['address'])
        if location.get('city'):
            address_parts.append(location['city'])
        if location.get('state'):
            address_parts.append(location['state'])
        if location.get('zipcode'):
            address_parts.append(str(location['zipcode']))
        delivery_address = ', '.join(address_parts) if address_parts else 'Unknown Address'
    else:
        delivery_address = 'Unknown Address'

    group_link = result.get('group_link', '')

    # Add Group Order Link field
    if group_link:
        embed.add_field(
            name="🔗 Group Order Link",
            value=f"[Click to join order]({group_link})",
            inline=False
        )

    # Add Delivery Location field
    if delivery_address:
        embed.add_field(
            name="📍 Delivery Location",
            value=delivery_address,
            inline=False
        )

    # Add Restaurant field
    restaurant_text = restaurant_name
    if restaurant_url:
        restaurant_text = f"[{restaurant_name}]({restaurant_url}) - View on Uber Eats"

    embed.add_field(
        name="🏪 Restaurant",
        value=restaurant_text,
        inline=False
    )

    # Add Order Items field
    if cart_items:
        items_text = "\n".join(cart_items[:10])  # Limit to first 10 items
        if len(cart_items) > 10:
            items_text += f"\n... and {len(cart_items) - 10} more items"

        embed.add_field(
            name=f"🛒 Order Items ({len(cart_items)})",
            value=items_text,
            inline=False
        )

    # Add Subtotal field
    embed.add_field(
        name="💰 Subtotal",
        value=f"${calculated_subtotal:.2f}",
        inline=True
    )


    # Set footer
    embed.set_footer(text="QuickEats")

    return embed



# Function to save status embed data
async def save_status_embed_data():
    """Save status embed data to file."""
    try:
        with open(STATUS_EMBED_DATA_FILE, 'w') as f:
            json.dump(status_embed_data, f)
        logger.info(f"Saved status embed data: {status_embed_data}")
    except Exception as e:
        logger.error(f"Error saving status embed data: {e}")

# Function to load status embed data
async def load_status_embed_data():
    """Load status embed data from file."""
    global status_embed_data
    try:
        if os.path.exists(STATUS_EMBED_DATA_FILE):
            with open(STATUS_EMBED_DATA_FILE, 'r') as f:
                status_embed_data = json.load(f)
            logger.info(f"Loaded status embed data: {status_embed_data}")
        else:
            # Initialize with default message ID if file doesn't exist
            status_embed_data = {'message_id': 1388974034066214993}
            await save_status_embed_data()
            logger.info("Initialized status embed data with default message ID")
    except Exception as e:
        logger.error(f"Error loading status embed data: {e}")
        # Fallback to default
        status_embed_data = {'message_id': 1388974034066214993}







# Function to update the status message with clocked-in staff
async def update_status_message(guild):
    """Update the status message with the current clocked-in staff."""
    try:
        # Get the status channel
        status_channel = guild.get_channel(1340406893927075900)  # Status Channel
        if not status_channel:
            logger.error("Status channel not found")
            return False

        # Get the status message
        try:
            status_message = await status_channel.fetch_message(STATUS_MESSAGE_ID)
        except discord.NotFound:
            logger.error(f"Status message with ID {STATUS_MESSAGE_ID} not found")
            return False
        except Exception as e:
            logger.error(f"Error fetching status message: {e}")
            return False

        # Check if the store is open
        store_open = await is_store_open(guild)



        # Create the appropriate embed based on store status
        if store_open:
            embed = discord.Embed(
                title="🚀 QuickEats is Now Open!",
                description="**We are now accepting orders!** Place your order using the instructions below.",
                color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
            )

            # Add some spacing between title and content
            embed.description += "\n"

            # Add information about how to order
            embed.add_field(
                name="📋 How to Order",
                value=f"Check out <#{1340210714891128882}> for detailed instructions on how to place an order.",
                inline=False
            )

            # Add information about payment methods
            embed.add_field(
                name="💳 Payment Methods",
                value="We accept various payment methods including PayPal, Venmo, and Cash App.",
                inline=False
            )



            # Set image and footer
            embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1344614541195218975/a.jpg?ex=67c18d60&is=67c03be0&hm=31b0756c3bb9d314fecab34039c85025b4271a349a8d5f9a7267a4c74e9864bc&=&format=webp&width=947&height=541")
            embed.set_footer(text="QuickEats | Fast & Reliable Service")
            embed.timestamp = datetime.datetime.now()

        else:
            embed = discord.Embed(
                title="🔴 QuickEats is Now Closed!",
                description="**We are currently closed.** Please check back later for updates.",
                color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
            )

            # Add some spacing between title and content
            embed.description += "\n"

            # Set image and footer
            embed.set_image(url="https://media.discordapp.net/attachments/978162420423999498/1344614540758749245/ClosedBanner.jpg?ex=67fa3de0&is=67f8ec60&hm=b74d7be3b974fc13fa60f06fa6f241dc9f02816a50ac51dff4809dbfa5da0ecb&=&format=webp&width=1466&height=838")
            embed.set_footer(text="QuickEats | Currently Closed")
            embed.timestamp = datetime.datetime.now()

        # Update the message
        await status_message.edit(embed=embed)
        logger.info(f"Updated status message with {len(staff_mentions)} clocked-in staff members")
        return True

    except Exception as e:
        logger.error(f"Error updating status message: {e}")
        logger.error(traceback.format_exc())
        return False









class OrderTrackButton(discord.ui.View):
    def __init__(self, order_link: str):
        super().__init__()

        # Create a blue (primary) style button
        track_button = discord.ui.Button(
            label="🔎 Track Order",
            style=discord.ButtonStyle.primary,  # This makes it blue
            url=order_link
        )
        self.add_item(track_button)

async def get_session() -> aiohttp.ClientSession:
    """Get or create the global HTTP session."""
    global _http_session
    if _http_session is None or _http_session.closed:
        _http_session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=20, ttl_dns_cache=300)
        )
    return _http_session

async def get_latest_embed(channel) -> Optional[discord.Embed]:
    """Get the latest embed from a channel."""
    try:
        async for message in channel.history(limit=10):
            if message.embeds:
                return message.embeds[0]
        return None
    except Exception as e:
        logger.error(f"Error getting latest embed: {e}")
        return None

async def is_store_open(guild) -> bool:
    """Check if the store is open by examining the latest embed in the status channel."""
    try:
        status_channel = guild.get_channel(1340406893927075900)  # Status Channel
        if not status_channel:
            logger.error("Status channel not found")
            return False

        # Check channel name first (most reliable indicator)
        if "open" in status_channel.name.lower():
            return True

        # Check latest embed as fallback
        latest_embed = await get_latest_embed(status_channel)
        if latest_embed and latest_embed.title:
            # If the title contains "Open", the store is open
            return "Open" in latest_embed.title or "🟢" in latest_embed.title
        return False
    except Exception as e:
        logger.error(f"Error checking if store is open: {e}")
        return False

@bot.event
async def setup_hook():
    """Set up tasks before the bot starts."""
    # Initialize the HTTP session
    await get_session()

    # Record start time for uptime tracking
    bot.launch_time = time.time()

    # Initialize tracking tasks list
    bot.tracking_tasks = []

    # Load saved tracking data and resume tracking
    await load_tracking_data()



    # Load status embed data
    await load_status_embed_data()





    # Initialize background tasks list (for future use)
    bot.bg_tasks = []
    logger.info("<:check:1396231906521251882> Background tasks initialized")

async def get_queue_position(channel) -> int:
    """Get the queue position of a channel."""
    try:
        # Define queue category ID (you may need to adjust this)
        QUEUE_CATEGORY_ID = 1354242418211422419  # Replace with actual queue category ID

        if not hasattr(channel, 'guild'):
            return None

        queue_category = channel.guild.get_channel(QUEUE_CATEGORY_ID)
        if not queue_category:
            return None

        # Get all channels in queue, sorted by position
        queue_channels = sorted(queue_category.channels, key=lambda c: c.position)

        # Find position of current channel (1-based)
        for i, queue_channel in enumerate(queue_channels):
            if queue_channel.id == channel.id:
                return i + 1

        return None
    except Exception as e:
        logger.error(f"Error getting queue position: {e}")
        return None

async def get_total_queue_count() -> int:
    """Get the total number of channels in the queue."""
    try:
        # Define queue category ID (you may need to adjust this)
        QUEUE_CATEGORY_ID = 1354242418211422419  # Replace with actual queue category ID

        # Get the first guild (assuming single guild bot)
        guild = bot.guilds[0] if bot.guilds else None
        if not guild:
            return 0

        queue_category = guild.get_channel(QUEUE_CATEGORY_ID)
        if not queue_category:
            return 0

        return len(queue_category.channels)
    except Exception as e:
        logger.error(f"Error getting total queue count: {e}")
        return 0

async def save_tracking_data():
    """Save active tracking information to a file."""
    try:
        # Create a copy of the tracking data with only serializable information
        tracking_data = {}
        for order_id, data in active_tracking.items():
            tracking_data[order_id] = {
                'channel_id': data['channel_id'],
                'start_time': data['start_time'],
                'last_status': data.get('last_status'),
                'order_link': data.get('order_link'),
                'status_embed_message_id': data.get('status_embed_message_id'),
                'status_history': data.get('status_history', []),
                'delivery_ping_sent': data.get('delivery_ping_sent', False)
            }

        # Save to file
        with open(TRACKING_DATA_FILE, 'w') as f:
            json.dump(tracking_data, f)

        logger.info(f"Saved tracking data for {len(tracking_data)} orders")
    except Exception as e:
        logger.error(f"Error saving tracking data: {e}")
        logger.error(traceback.format_exc())

async def load_tracking_data():
    """Load tracking data from file and resume tracking."""
    global active_tracking

    try:
        # Check if the file exists
        if not os.path.exists(TRACKING_DATA_FILE):
            logger.info("No tracking data file found")
            return

        # Load data from file
        with open(TRACKING_DATA_FILE, 'r') as f:
            tracking_data = json.load(f)

        if not tracking_data:
            logger.info("No tracking data to resume")
            return

        logger.info(f"Found tracking data for {len(tracking_data)} orders")

        # Resume tracking for each order
        for order_id, data in tracking_data.items():
            try:
                # Get the channel
                channel_id = data['channel_id']
                channel = bot.get_channel(int(channel_id))

                # If channel not found in cache, try fetching from API
                if not channel:
                    try:
                        channel = await bot.fetch_channel(int(channel_id))
                        logger.info(f"Successfully fetched channel {channel_id} from API for order {order_id}")
                    except discord.NotFound:
                        logger.warning(f"Channel {channel_id} not found (deleted) for order {order_id}")
                        continue
                    except discord.Forbidden:
                        logger.warning(f"No permission to access channel {channel_id} for order {order_id}")
                        continue
                    except Exception as e:
                        logger.warning(f"Error fetching channel {channel_id} for order {order_id}: {e}")
                        continue

                # Store in active tracking
                active_tracking[order_id] = {
                    'channel_id': channel_id,
                    'start_time': data['start_time'],
                    'last_status': data.get('last_status'),
                    'order_link': data.get('order_link'),
                    'status_embed_message_id': data.get('status_embed_message_id'),
                    'status_history': data.get('status_history', []),
                    'delivery_ping_sent': data.get('delivery_ping_sent', False)
                }

                # Get a session
                session = await get_session()

                # Resume tracking
                order_link = data.get('order_link', f"https://www.ubereats.com/orders/{order_id}")
                tracking_task = asyncio.create_task(
                    track_order_status(order_id, channel, session, active_tracking_dict=active_tracking, save_tracking_func=save_tracking_data)
                )

                # Store the task
                if not hasattr(bot, 'tracking_tasks'):
                    bot.tracking_tasks = []
                bot.tracking_tasks.append(tracking_task)

                # Notify in the channel
                await channel.send(f"<:check:1396231906521251882> Resumed tracking for order {order_id}")

                logger.info(f"Resumed tracking for order {order_id} in channel {channel_id}")
            except Exception as e:
                logger.error(f"Error resuming tracking for order {order_id}: {e}")
                logger.error(traceback.format_exc())
    except Exception as e:
        logger.error(f"Error loading tracking data: {e}")
        logger.error(traceback.format_exc())

@bot.event
async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Handle app command errors."""
    # Handle errors
    logger.error(f"Command error: {error}")
    traceback.print_exception(type(error), error, error.__traceback__)

@bot.event
async def on_ready():
    """Called when the bot is ready."""
    logger.info(f"<:check:1396231906521251882> Logged in as {bot.user}")
    logger.info(f"Command mode: {'Global' if COMMANDS_GLOBAL else 'Guild-only'}")

    # Memory usage info removed - psutil dependency deleted

    try:
        if COMMANDS_GLOBAL:
            synced = await bot.tree.sync()
            logger.info(f"<:check:1396231906521251882> Synced {len(synced)} command(s) globally")
        else:
            bot.tree.copy_global_to(guild=GUILD)
            synced = await bot.tree.sync(guild=GUILD)
            logger.info(f"<:check:1396231906521251882> Synced {len(synced)} command(s) to guild")
    except Exception as e:
        logger.error(f"<:cancel:1396231869980348467> Failed to sync commands: {e}")
        logger.error(traceback.format_exc())

@bot.event
async def on_message(message):
    # Skip messages from the bot itself
    if message.author == bot.user:
        return



    try:
        # Extract group link
        group_link = await extract_group_link(message)
        if not group_link:
            return

        # Normalize the link to ensure consistent matching
        # Remove any trailing parameters or fragments
        if '?' in group_link:
            group_link = group_link.split('?')[0]
        if '#' in group_link:
            group_link = group_link.split('#')[0]

        # Ensure the link ends with /join for consistency
        if not group_link.endswith('/join'):
            if group_link.endswith('/'):
                group_link = group_link + 'join'
            else:
                group_link = group_link + '/join'

        logger.info(f"<:search:1396233145048629269> Group order link detected: {group_link}")

        # Send a processing message with a nice embed
        processing_message = await message.channel.send(embed=create_processing_embed())

        # Process group order
        result = await process_group_order(group_link)

        # Add the group link to the result dictionary
        if isinstance(result, dict):
            result['group_link'] = group_link

        # Handle locked/error cases
        if isinstance(result, dict) and 'error' in result:
            if result['error'].get('type') == 'LOCKED_ORDER':
                await processing_message.delete()
                await message.channel.send(embed=create_locked_order_embed())
                return

        if not result:
            await processing_message.delete()
            await message.channel.send(embed=create_locked_order_embed())
            return

        # Process order summary without promo checking or pricing calculations

        # Process cart items
        cart_items = []
        calculated_subtotal = 0

        if 'cart_items' in result:
            for item in result['cart_items']:
                price = item.get('price', 0)
                quantity = item.get('quantity', 1)
                title = item.get('title', 'Unknown Item')
                cart_items.append(f"{title} x{quantity} (${price/100:.2f})")
                calculated_subtotal += (price * quantity) / 100

        # PriceCheckerV2 Integration - FULLY ENABLED
        logger.info("Running PriceCheckerV2 analysis...")
        try:
            pricing_analysis = await quickbites_bot_price_analysis(group_link)
            if pricing_analysis.get('success'):
                logger.info(f"PriceCheckerV2 analysis successful: {pricing_analysis.get('message', '')}")
            else:
                logger.warning(f"PriceCheckerV2 analysis failed: {pricing_analysis.get('error', 'Unknown error')}")
        except Exception as e:
            logger.error(f"PriceCheckerV2 analysis exception: {str(e)}")
            pricing_analysis = {'success': False, 'error': f'Analysis failed: {str(e)}'}

        # Delete the processing message
        await processing_message.delete()

        # Create and send complex order summary with pricing analysis
        summary_embed = create_order_summary_embed(result, cart_items, pricing_analysis)
        await message.channel.send(embed=summary_embed)

        # Send complex success message with pricing analysis results
        if pricing_analysis and pricing_analysis.get('success'):
            pricing_data = pricing_analysis.get('pricing_comparison', {})
            price_message = pricing_analysis.get('message', '')

            # Calculate QuickEats pricing for success message
            pricing_breakdown = pricing_data.get('pricing_breakdown', {})
            original_uber_total = pricing_breakdown.get('total', 0)

            # Apply QuickEats pricing transformations
            original_subtotal = pricing_breakdown.get('subtotal', 0)
            quickeats_discount = 25.00
            discounted_subtotal = max(0.0, original_subtotal - quickeats_discount)

            original_taxes = pricing_breakdown.get('taxes', 0)
            quickeats_fee = 10.00
            updated_taxes = original_taxes + quickeats_fee

            quickeats_total = discounted_subtotal + updated_taxes

            # Create pricing message showing Uber total → QuickEats total
            quickeats_price_message = f"${original_uber_total:.2f} → ${quickeats_total:.2f}"

            success_embed = discord.Embed(
                title="<:check:1396231906521251882> Order Processed Successfully",
                description=f"Your order has been processed\n\n**Pricing Analysis:** {quickeats_price_message}\n\n**Next Steps**\nPlease wait patiently. A Chef or a member of the Support Team will assist you shortly.",
                color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
            )

            # Add a thumbnail for a modern look
            success_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057668692705442/newfood.gif?ex=6879f4fb&is=6878a37b&hm=1e6dae283bd9828646e04b0dc1bdbeb59e3718929ec073fdaaa4163d71a54776&=")  # Food/order icon
            success_embed.set_footer(text="Thank you for using Quick Eats")
            await message.channel.send(embed=success_embed)

        elif pricing_analysis and not pricing_analysis.get('success'):
            # Show warning if pricing analysis failed but still processed the order
            warning_embed = discord.Embed(
                title="<:warning:1396233220936175648> Order Processed (Pricing Analysis Failed)",
                description=f"Your order has been processed, but pricing analysis failed.\n\n**Error:** {pricing_analysis.get('error', 'Unknown error')}\n\n**Next Steps**\nPlease wait patiently. A Chef or Waiter will assist you shortly.",
                color=discord.Color.from_rgb(254, 231, 92)  # Yellow
            )
            warning_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057668692705442/newfood.gif?ex=6879f4fb&is=6878a37b&hm=1e6dae283bd9828646e04b0dc1bdbeb59e3718929ec073fdaaa4163d71a54776&=")
            warning_embed.set_footer(text="Quick Eats | Basic processing completed")
            await message.channel.send(embed=warning_embed)



    except Exception as e:
        logger.error(f"Error processing group order link: {str(e)}")
        logger.error(traceback.format_exc())

        # Try to delete the processing message if it exists
        try:
            if 'processing_message' in locals():
                await processing_message.delete()
        except:
            pass

        # Send a nicely formatted error message
        await message.channel.send(embed=create_error_embed(str(e)))





@bot.tree.command(
    name="track",
    description="Track an Uber order status",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    order_id="The Uber order ID to track"
)

async def track_command(interaction: discord.Interaction, order_id: str):
    # Track command execution time
    start_time = time.time()

    try:
        await interaction.response.defer(ephemeral=True)
        logger.info(f"📌 `/track` command triggered by {interaction.user} for order: {order_id}")

        # Construct the order link
        orderlink = f"https://www.ubereats.com/orders/{order_id}"

        # Get the HTTP session
        session = await get_session()

        # Fetch order details with the shared session using the global function
        order_details = await fetch_order_details(order_id, session)

        if order_details:
            # Extract subtotal for validation
            subtotal = 0.0
            try:
                # Try to extract subtotal from order details
                if 'subtotal' in order_details:
                    subtotal_str = order_details['subtotal']
                    # Remove currency symbol and convert to float
                    subtotal = float(re.sub(r'[^\d.]', '', subtotal_str))
                    logger.info(f"Extracted subtotal: ${subtotal:.2f}")
            except Exception as e:
                logger.error(f"Error extracting subtotal: {e}")
                logger.error(traceback.format_exc())



            # Create the success embed with enhanced modern design
            order_embed = discord.Embed(
                title="<:startup:1396233376045863015> Order Tracking Initiated",
                description="Your order has been successfully placed and is now being tracked!",
                color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
            )

            # Set a nice thumbnail - using the original tracking thumbnail
            order_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057664729088122/newphone_burger.gif?ex=6879f4fa&is=6878a37a&hm=b1a0cd125bad80a6cb31cfca8ef458120a20bcf8bfde6bfd61872880214c5a03&=")

            # Add spacing to the description
            order_embed.description += "\n\n"

            # Restaurant section with emoji (only if not unknown)
            store_name = order_details.get('store', 'Unknown Store')
            if store_name != 'Unknown Store' and store_name != 'N/A' and not ('item' in store_name.lower() or '$' in store_name):
                order_embed.add_field(
                    name="<:promotion:1396232486589304962> Restaurant",
                    value=f"**{store_name}**",
                    inline=False
                )

            # ETA section with emoji
            order_embed.add_field(
                name="<:clock:1396232112843001866> Estimated Arrival",
                value=f"**{order_details['eta']}**",
                inline=True
            )

            # Customer section with emoji
            customer_name = order_details.get('customer', 'Unknown')
            if customer_name == 'N/A' or not customer_name:
                customer_name = interaction.user.display_name

            order_embed.add_field(
                name="<:personalinformation:1396233335662972959> Customer",
                value=f"**{customer_name}**",
                inline=True
            )

            # Order items section with better formatting
            items_text = order_details['items']
            formatted_items = "\n".join([f"╰・ *{item.strip()}*" for item in items_text.split('•') if item.strip()])
            order_embed.add_field(
                name="<:shoppingcart:1396233174735913093> Order Items",
                value=formatted_items if formatted_items else "No items found",
                inline=False
            )

            # Delivery address section with code block formatting
            order_embed.add_field(
                name="<:placeholder:1396231754335129620> Delivery Address",
                value=f"```{order_details['address']}```",
                inline=False
            )

            # Order link with button-like formatting
            order_embed.add_field(
                name="<:link:1396231996887531630> Order Link",
                value=f"[**Click to view order**]({orderlink})",
                inline=False
            )

            # Add footer with tracking info and timestamp
            order_embed.set_footer(text="Order is being tracked automatically | Quick Eats")
            order_embed.timestamp = datetime.datetime.now()

            # Create view with tracking button
            view = OrderTrackButton(orderlink)

            # Send the embed with the view and start tracking
            await interaction.followup.send(embed=order_embed, view=view)

            # Store tracking information
            active_tracking[order_id] = {
                'channel_id': interaction.channel.id,
                'start_time': time.time(),
                'last_status': None,
                'order_link': orderlink,
                'status_embed_message_id': None,
                'status_history': [],
                'delivery_ping_sent': False
            }

            # Create initial persistent tracking embed
            from quickbitesbot.common.bot import OrderStatusButtons

            initial_embed = discord.Embed(
                title="<:car:1396233618908512438> Order Tracking",
                description=f"**Order ID:** `{order_id}`\n**Status:** Initializing tracking...",
                color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
            )

            # Add queue position if channel is in queue
            queue_position = await get_queue_position(interaction.channel)
            if queue_position:
                total_queue = await get_total_queue_count()
                initial_embed.add_field(
                    name="📊 Queue Position",
                    value=f"#{queue_position} of {total_queue}",
                    inline=True
                )

            initial_embed.add_field(
                name="📋 Status History",
                value="No status updates yet.",
                inline=False
            )

            initial_embed.set_footer(text="Quick Eats | Order Tracking")
            initial_embed.timestamp = discord.utils.utcnow()

            # Create view with Status History button
            view_buttons = OrderStatusButtons(order_id, active_tracking)

            # Send initial tracking embed and store message ID
            initial_message = await interaction.followup.send(embed=initial_embed, view=view_buttons)
            active_tracking[order_id]['status_embed_message_id'] = initial_message.id

            # Save tracking data to file
            await save_tracking_data()

            # Start tracking in a background task and store it for cleanup
            logger.info(f"QUICKEATSBOT: Creating tracking task for order_id: {order_id}")
            logger.info(f"Channel: {interaction.channel.name if hasattr(interaction.channel, 'name') else 'Unknown channel'}")
            logger.info(f"Session: {session}")

            try:
                # Use the global track_order_status function
                tracking_task = asyncio.create_task(track_order_status(order_id, interaction.channel, session, active_tracking_dict=active_tracking, save_tracking_func=save_tracking_data))
                if not hasattr(bot, 'tracking_tasks'):
                    bot.tracking_tasks = []
                bot.tracking_tasks.append(tracking_task)
                logger.info(f"Successfully created and stored tracking task for order_id: {order_id}")
            except Exception as e:
                logger.error(f"Error creating tracking task: {e}")
                logger.error(traceback.format_exc())
                await interaction.followup.send(f"⚠️ Error starting order tracking: {str(e)}")
        else:
            await interaction.followup.send("<:cancel:1396231869980348467> Failed to fetch order details.", ephemeral=True)
            logger.error(f"Failed to fetch order details for order ID: {order_id}")

        # Metrics tracking removed - /metrics command deleted
        execution_time = time.time() - start_time
        logger.debug(f"Command track executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in track command: {e}")
        logger.error(traceback.format_exc())
        if not interaction.response.is_done():
            await interaction.response.send_message(f"<:cancel:1396231869980348467> An error occurred: {e}", ephemeral=True)



















@bot.tree.command(
    name="ordersuccess",
    description="Track a successful order using order link",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True, read_message_history=True, read_messages=True, view_channel=True)
@app_commands.describe(
    orderlink="The order link to track"
)

async def ordersuccess_slash(
    interaction: discord.Interaction,
    orderlink: str
):
    """Slash command to track a successful order."""
    # Track command execution time
    start_time = time.time()

    try:
        logger.info(f"📌 `/ordersuccess` was triggered by {interaction.user} with order link: {orderlink}")
        await interaction.response.defer()

        # Handle channel renaming
        current_channel = interaction.channel
        if current_channel.name.startswith(("nelo4317-", "itscerv-", "_glitchyz-", "ticket-")):
            try:
                ticket_number = re.sub(r"^(nelo4317-|itscerv-|_glitchyz-|ticket-)", "", current_channel.name)
                new_name = f"{ticket_number}-delivering"
                await current_channel.edit(name=new_name)
                logger.info(f"<:check:1396231906521251882> Successfully renamed channel to {new_name}")


            except Exception as e:
                logger.error(f"<:cancel:1396231869980348467> Failed to rename channel: {str(e)}")
                logger.error(traceback.format_exc())

        # Extract order ID and fetch details
        order_id = re.search(r"orders/([a-f0-9-]+)", orderlink)
        if order_id:
            order_id = order_id.group(1)

            # Get the HTTP session
            session = await get_session()

            # Fetch order details with the shared session using the global function
            order_details = await fetch_order_details(order_id, session)

            if order_details:
                # Create the success embed with enhanced modern design
                order_embed = discord.Embed(
                    title="<:startup:1396233376045863015> Order Tracking Initiated",
                    description="Your order has been successfully placed and is now being tracked!",
                    color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
                )

                # Set a nice thumbnail - using the original tracking thumbnail
                order_embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057664729088122/newphone_burger.gif?ex=6879f4fa&is=6878a37a&hm=b1a0cd125bad80a6cb31cfca8ef458120a20bcf8bfde6bfd61872880214c5a03&=")

                # Add spacing to the description
                order_embed.description += "\n\n"

                # Restaurant section with emoji (only if not unknown)
                store_name = order_details.get('store', 'Unknown Store')
                if store_name != 'Unknown Store' and store_name != 'N/A' and not ('item' in store_name.lower() or '$' in store_name):
                    order_embed.add_field(
                        name="<:promotion:1396232486589304962> Restaurant",
                        value=f"**{store_name}**",
                        inline=False
                    )

                # ETA section with emoji
                order_embed.add_field(
                    name="<:clock:1396232112843001866> Estimated Arrival",
                    value=f"**{order_details['eta']}**",
                    inline=True
                )

                # Customer section with emoji
                customer_name = order_details.get('customer', 'Unknown')
                if customer_name == 'N/A' or not customer_name:
                    customer_name = interaction.user.display_name

                order_embed.add_field(
                    name="<:personalinformation:1396233335662972959> Customer",
                    value=f"**{customer_name}**",
                    inline=True
                )

                # Add spacing between sections
                # No divider needed

                # Order items section with better formatting
                items_text = order_details['items']
                formatted_items = "\n".join([f"╰・ *{item.strip()}*" for item in items_text.split('•') if item.strip()])
                order_embed.add_field(
                    name="<:shoppingcart:1396233174735913093> Order Items",
                    value=formatted_items if formatted_items else "No items found",
                    inline=False
                )

                # Add spacing between sections
                # No divider needed

                # Delivery address section with code block formatting
                order_embed.add_field(
                    name="<:placeholder:1396231754335129620> Delivery Address",
                    value=f"```{order_details['address']}```",
                    inline=False
                )

                # Order link with button-like formatting
                order_embed.add_field(
                    name="<:link:1396231996887531630> Order Link",
                    value=f"[**Click to view order**]({orderlink})",
                    inline=False
                )

                # Add footer with tracking info and timestamp
                order_embed.set_footer(text="Order is being tracked automatically | Quick Eats")
                order_embed.timestamp = datetime.datetime.now()

                # Create view with tracking button
                view = OrderTrackButton(orderlink)

                # Send the embed with the view and start tracking
                await interaction.followup.send(embed=order_embed, view=view)

                # Store tracking information
                active_tracking[order_id] = {
                    'channel_id': interaction.channel.id,
                    'start_time': time.time(),
                    'last_status': None,
                    'order_link': orderlink,
                    'status_embed_message_id': None,
                    'status_history': [],
                    'delivery_ping_sent': False
                }

                # Create initial persistent tracking embed
                initial_embed = discord.Embed(
                    title="<:car:1396233618908512438> Order Tracking",
                    description=f"**Order ID:** `{order_id}`\n**Status:** Initializing tracking...",
                    color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
                )

                # Add queue position if channel is in queue
                queue_position = await get_queue_position(interaction.channel)
                if queue_position:
                    total_queue = await get_total_queue_count()
                    initial_embed.add_field(
                        name="📊 Queue Position",
                        value=f"#{queue_position} of {total_queue}",
                        inline=True
                    )

                initial_embed.add_field(
                    name="📋 Status History",
                    value="No status updates yet.",
                    inline=False
                )

                initial_embed.set_footer(text="Quick Eats | Order Tracking")
                initial_embed.timestamp = discord.utils.utcnow()

                # Create view with Status History button
                view_buttons = OrderStatusButtons(order_id, active_tracking)

                # Send initial tracking embed and store message ID
                initial_message = await interaction.followup.send(embed=initial_embed, view=view_buttons)
                active_tracking[order_id]['status_embed_message_id'] = initial_message.id

                # Save tracking data to file
                await save_tracking_data()

                # Start tracking in a background task and store it for cleanup
                logger.info(f"QUICKEATSBOT: Creating tracking task for order_id: {order_id}")
                logger.info(f"Channel: {interaction.channel.name if hasattr(interaction.channel, 'name') else 'Unknown channel'}")
                logger.info(f"Session: {session}")

                try:
                    # Use the global track_order_status function
                    tracking_task = asyncio.create_task(track_order_status(order_id, interaction.channel, session, active_tracking_dict=active_tracking, save_tracking_func=save_tracking_data))
                    if not hasattr(bot, 'tracking_tasks'):
                        bot.tracking_tasks = []
                    bot.tracking_tasks.append(tracking_task)
                    logger.info(f"Successfully created and stored tracking task for order_id: {order_id}")
                except Exception as e:
                    logger.error(f"Error creating tracking task: {e}")
                    logger.error(traceback.format_exc())
                    await interaction.followup.send(f"⚠️ Error starting order tracking: {str(e)}")
            else:
                await interaction.followup.send("<:cancel:1396231869980348467> Failed to fetch order details.", ephemeral=True)
        else:
            await interaction.followup.send("<:cancel:1396231869980348467> Invalid order link format.", ephemeral=True)
            return

        logger.info("<:check:1396231906521251882> Order tracking initiated.")

        # Metrics tracking removed - /metrics command deleted
        execution_time = time.time() - start_time
        logger.debug(f"Command ordersuccess executed in {execution_time:.4f}s")
    except Exception as e:
        logger.error(f"Error in ordersuccess command: {e}")
        logger.error(traceback.format_exc())
        try:
            if interaction.response.is_done():
                await interaction.followup.send(f"<:cancel:1396231869980348467> An error occurred: {e}", ephemeral=True)
            else:
                await interaction.response.send_message(f"<:cancel:1396231869980348467> An error occurred: {e}", ephemeral=True)
        except Exception:
            pass













# Legacy prefix command error handler removed - bot now uses slash commands only

# Payment Information Commands
@bot.tree.command(
    name="jimmy",
    description="Display Jimmy's payment methods and contact information",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
async def jimmy_payment(interaction: discord.Interaction):
    """Display Jimmy's payment information."""
    try:
        embed = discord.Embed(
            title="💳 Jimmy's Payment Information",
            description="Choose your preferred payment method below:",
            color=discord.Color.from_rgb(87, 242, 135)  # QuickEats Green
        )

        # Payment Methods
        payment_methods = (
            "**ApplePay:** (917) 445-1420\n"
            "**CashApp:** $isaeed224\n"
            "**Venmo:** @rooksol\n"
            "**PayPal:** <EMAIL>\n"
            "**Zelle:** 6318291307"
        )

        embed.add_field(
            name="💰 Payment Methods",
            value=payment_methods,
            inline=False
        )

        # Important warning
        embed.add_field(
            name="⚠️ IMPORTANT",
            value="Always use Friends & Family (F&F) for PayPal/Venmo payments",
            inline=False
        )

        # Footer
        embed.set_footer(text="QuickEats Payment System")

        # Thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057669368119360/newtap_to_pay.gif?ex=687d40bb&is=687bef3b&hm=11a9f940c56206b60547a692820ded36b8712b60799204ff5fb12eeee42b7cba&=")

        await interaction.response.send_message(embed=embed)

    except Exception as e:
        logger.error(f"Error in jimmy_payment command: {e}")
        logger.error(traceback.format_exc())
        await interaction.response.send_message(
            f"<:cancel:1396231869980348467> An error occurred while displaying payment information.",
            ephemeral=True
        )

@bot.tree.command(
    name="sham",
    description="Display Sham's payment methods and contact information",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
async def sham_payment(interaction: discord.Interaction):
    """Display Sham's payment information."""
    try:
        embed = discord.Embed(
            title="💳 Sham's Payment Information",
            description="Choose your preferred payment method below:",
            color=discord.Color.from_rgb(87, 242, 135)  # QuickEats Green
        )

        # Card & Digital Payments with Stripe
        embed.add_field(
            name="💳 Card & Digital Payments",
            value="[Pay with Stripe](https://buy.stripe.com/14k02t418gEwbpS6oo)",
            inline=False
        )

        # Cryptocurrency section
        crypto_methods = (
            "**BTC:** `**********************************`\n"
            "**LTC:** `MH1KG3LoJ35w4YQ1gHMTceNXg1Rdv1e6H1`\n"
            "**ETH:** `******************************************`\n"
            "**SOL:** `DsqasZShX5sg4kX18teS7S2ayrhDWhXUfa941v3agjMy`"
        )

        embed.add_field(
            name="₿ Cryptocurrency",
            value=crypto_methods,
            inline=False
        )

        # Important warning
        embed.add_field(
            name="⚠️ IMPORTANT",
            value="Always use Friends & Family (F&F) for PayPal/Venmo payments",
            inline=False
        )

        # Footer
        embed.set_footer(text="QuickEats Payment System")

        # Thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057669368119360/newtap_to_pay.gif?ex=687d40bb&is=687bef3b&hm=11a9f940c56206b60547a692820ded36b8712b60799204ff5fb12eeee42b7cba&=")

        await interaction.response.send_message(embed=embed)

    except Exception as e:
        logger.error(f"Error in sham_payment command: {e}")
        logger.error(traceback.format_exc())
        await interaction.response.send_message(
            f"<:cancel:1396231869980348467> An error occurred while displaying payment information.",
            ephemeral=True
        )

@bot.tree.command(
    name="surged",
    description="Display Surged's payment methods and contact information",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
async def surged_payment(interaction: discord.Interaction):
    """Display Surged's payment information."""
    try:
        embed = discord.Embed(
            title="💳 Surged's Payment Information",
            description="Choose your preferred payment method below:",
            color=discord.Color.from_rgb(87, 242, 135)  # QuickEats Green
        )

        # Card & Digital Payments with Stripe
        embed.add_field(
            name="💳 Card & Digital Payments",
            value="[Pay with Stripe](https://buy.stripe.com/bJe14o5AAdu6dTwcgGcIE04)",
            inline=False
        )

        # Other Payment Methods
        other_methods = (
            "**Apple Pay:** 6202146368\n"
            "**PayPal:** <EMAIL>\n"
            "**Venmo:** @tecsupportmo\n"
            "**Zelle:** 6202146368"
        )

        embed.add_field(
            name="💰 Other Payment Methods",
            value=other_methods,
            inline=False
        )

        # Important warning
        embed.add_field(
            name="⚠️ IMPORTANT",
            value="Always use Friends & Family (F&F) for PayPal/Venmo payments",
            inline=False
        )

        # Footer
        embed.set_footer(text="QuickEats Payment System")

        # Thumbnail
        embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057669368119360/newtap_to_pay.gif?ex=687d40bb&is=687bef3b&hm=11a9f940c56206b60547a692820ded36b8712b60799204ff5fb12eeee42b7cba&=")

        await interaction.response.send_message(embed=embed)

    except Exception as e:
        logger.error(f"Error in surged_payment command: {e}")
        logger.error(traceback.format_exc())
        await interaction.response.send_message(
            f"<:cancel:1396231869980348467> An error occurred while displaying payment information.",
            ephemeral=True
        )

# Application command error handler
@bot.tree.error
async def on_app_command_error(interaction: discord.Interaction, error: app_commands.AppCommandError):
    """Handle application command errors."""
    if isinstance(error, app_commands.CommandOnCooldown):
        # Simply inform the user about the cooldown without queueing
        command_name = interaction.command.name if interaction.command else "unknown"

        # Provide specific message for bulk channel management commands
        if command_name in []:  # No bulk commands remaining
            message = (
                f"⏳ **Bulk Channel Management Cooldown**\n"
                f"The `/{command_name}` command is on cooldown to prevent rate limiting.\n"
                f"Please wait **{error.retry_after:.1f} seconds** before using this command again.\n\n"
                f"*This cooldown helps protect the bot from Discord API rate limits.*"
            )
        else:
            message = f"⏳ Command `/{command_name}` is on cooldown. Please try again in {error.retry_after:.1f} seconds."

        await interaction.response.send_message(message, ephemeral=True)
    elif isinstance(error, app_commands.MissingPermissions):
        await interaction.response.send_message(
            "⛔ You don't have permission to use this command.",
            ephemeral=True
        )
    else:
        logger.error(f"App command error: {error}")
        logger.error(traceback.format_exc())

        # Try to respond if not already responded
        try:
            if interaction.response.is_done():
                await interaction.followup.send(
                    f"<:cancel:1396231869980348467> An error occurred: {error}",
                    ephemeral=True
                )
            else:
                await interaction.response.send_message(
                    f"<:cancel:1396231869980348467> An error occurred: {error}",
                    ephemeral=True
                )
        except Exception as e:
            logger.error(f"Failed to send error message: {e}")

# Command metrics decorator removed - /metrics command deleted











# Graceful shutdown
async def cleanup():
    """Clean up resources before shutdown."""
    logger.info("Cleaning up resources...")

    # Save tracking data before shutdown
    await save_tracking_data()
    logger.info("Saved tracking data for resuming on next startup")





    # Cancel background tasks
    if hasattr(bot, 'bg_tasks'):
        for task in bot.bg_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

    # Cancel tracking tasks
    if hasattr(bot, 'tracking_tasks'):
        for task in bot.tracking_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

    # Close HTTP session
    global _http_session
    if _http_session and not _http_session.closed:
        await _http_session.close()
        logger.info("HTTP session closed")

def run_bot():
    try:
        # Run the bot with proper signal handling
        bot.run(DISCORD_BOT_TOKEN, log_handler=None)
    except KeyboardInterrupt:
        logger.info("Bot stopped by keyboard interrupt")
    except Exception as e:
        logger.error(f"Error running bot: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    run_bot()