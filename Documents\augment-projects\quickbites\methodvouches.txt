# QuickEats Discord Bot Vouch Tracking System - Technical Specification

## 1. System Overview

The vouch tracking system is a comprehensive point-based reward system that automatically monitors a designated Discord channel for image attachments (vouches) and awards points to users. The system provides user commands for checking points and viewing leaderboards, plus administrative commands for manual point management.

### Key Features:
- **Automatic Point Award**: Detects image attachments in designated channel and awards 1 point per image
- **Persistent Storage**: JSON-based database that survives bot restarts
- **User Commands**: Point checking and leaderboard viewing
- **Admin Commands**: Manual point addition/removal with role-based permissions
- **Real-time Feedback**: Instant confirmation embeds when points are awarded

### Workflow:
1. User posts image in designated vouch channel
2. <PERSON><PERSON> detects image attachment via event handler
3. System awards 1 point and updates JSON database
4. <PERSON><PERSON> replies with confirmation embed showing new point total
5. Users can check points and view leaderboards via slash commands
6. <PERSON><PERSON> can manually adjust points as needed

## 2. Database Structure

### JSON File Format
**File Location**: `vouch_data.json` (in bot's root directory)

```json
{
  "user_id_string": {
    "points": 15,
    "vouches": [
      {
        "timestamp": "2025-01-21T00:30:00.123456",
        "type": "image_vouch"
      },
      {
        "timestamp": "2025-01-21T01:15:30.789012",
        "type": "manual_adjustment",
        "amount": 5
      }
    ]
  },
  "987654321": {
    "points": 8,
    "vouches": [
      {
        "timestamp": "2025-01-21T02:45:15.456789",
        "type": "image_vouch"
      }
    ]
  }
}
```

### Data Schema
- **Root Level**: Dictionary with user IDs as string keys
- **User Object**:
  - `points` (int): Current total points for the user
  - `vouches` (array): Historical record of all point-earning activities
- **Vouch Entry**:
  - `timestamp` (string): ISO format timestamp of the activity
  - `type` (string): Type of activity (`"image_vouch"` or `"manual_adjustment"`)
  - `amount` (int, optional): For manual adjustments, the amount added/removed

## 3. Core Functions

### 3.1 Data Persistence Functions

```python
def load_vouch_data() -> Dict[str, Any]:
    """
    Load vouch data from JSON file.
    
    Returns:
        Dict containing all user vouch data, empty dict if file doesn't exist
        
    Error Handling:
        - Returns empty dict if file doesn't exist
        - Returns empty dict if JSON is corrupted
        - Logs warnings for file issues
    """
    if VOUCH_DATA_FILE.exists():
        try:
            with open(VOUCH_DATA_FILE, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, FileNotFoundError):
            logger.warning("Failed to load vouch data, starting fresh")
    return {}

def save_vouch_data(data: Dict[str, Any]) -> None:
    """
    Save vouch data to JSON file.
    
    Args:
        data: Dictionary containing all user vouch data
        
    Error Handling:
        - Logs error if file write fails
        - Does not raise exceptions (graceful degradation)
    """
    try:
        with open(VOUCH_DATA_FILE, 'w') as f:
            json.dump(data, f, indent=2)
    except Exception as e:
        logger.error(f"Failed to save vouch data: {e}")
```

### 3.2 Point Management Functions

```python
def add_vouch_point(user_id: int) -> int:
    """
    Add a vouch point to a user and record the activity.
    
    Args:
        user_id: Discord user ID (integer)
        
    Returns:
        int: New total points for the user
        
    Side Effects:
        - Creates user entry if doesn't exist
        - Increments user's point count by 1
        - Adds vouch entry to user's history
        - Saves data to JSON file
    """
    data = load_vouch_data()
    user_id_str = str(user_id)
    
    if user_id_str not in data:
        data[user_id_str] = {
            'points': 0,
            'vouches': []
        }
    
    data[user_id_str]['points'] += 1
    data[user_id_str]['vouches'].append({
        'timestamp': datetime.datetime.now().isoformat(),
        'type': 'image_vouch'
    })
    
    save_vouch_data(data)
    return data[user_id_str]['points']

def get_user_points(user_id: int) -> int:
    """
    Get a user's current vouch points.
    
    Args:
        user_id: Discord user ID (integer)
        
    Returns:
        int: Current points (0 if user doesn't exist)
    """
    data = load_vouch_data()
    user_id_str = str(user_id)
    return data.get(user_id_str, {}).get('points', 0)

def modify_user_points(user_id: int, amount: int) -> int:
    """
    Modify a user's points by a specific amount (for admin commands).
    
    Args:
        user_id: Discord user ID (integer)
        amount: Points to add (positive) or remove (negative)
        
    Returns:
        int: New total points (minimum 0)
        
    Side Effects:
        - Creates user entry if doesn't exist
        - Ensures points never go below 0
        - Records manual adjustment in user's history
        - Saves data to JSON file
    """
    data = load_vouch_data()
    user_id_str = str(user_id)
    
    if user_id_str not in data:
        data[user_id_str] = {
            'points': 0,
            'vouches': []
        }
    
    data[user_id_str]['points'] = max(0, data[user_id_str]['points'] + amount)
    data[user_id_str]['vouches'].append({
        'timestamp': datetime.datetime.now().isoformat(),
        'type': 'manual_adjustment',
        'amount': amount
    })
    
    save_vouch_data(data)
    return data[user_id_str]['points']

def get_leaderboard() -> List[Tuple[str, Dict[str, Any]]]:
    """
    Get the top 10 users by vouch points.
    
    Returns:
        List of tuples: [(user_id_string, user_data), ...]
        Sorted by points (descending), limited to top 10
    """
    data = load_vouch_data()
    sorted_users = sorted(data.items(), key=lambda x: x[1]['points'], reverse=True)
    return sorted_users[:10]
```

## 4. Event Handler

### 4.1 Automatic Vouch Detection

```python
async def on_message_vouch_tracking(message: discord.Message) -> None:
    """
    Handle vouch tracking for image attachments in the vouch channel.
    
    Logic Flow:
    1. Check if message is in designated vouch channel
    2. Skip if message is from a bot
    3. Check if message has image attachments
    4. Award point and send confirmation embed
    
    Args:
        message: Discord message object
        
    Channel Filter:
        - Only processes messages from VOUCH_CHANNEL_ID
        - Ignores messages from bots (message.author.bot)
        
    Image Detection:
        - Checks message.attachments for any attachments
        - Validates attachment.content_type starts with 'image/'
        - Awards 1 point per message (not per image)
        
    Response:
        - Replies to original message with embed
        - Uses QuickEats green color (87, 242, 135)
        - Shows "+1 point (current points: X)" format
    """
    # Skip if not in vouch channel or from bot
    if message.channel.id != VOUCH_CHANNEL_ID or message.author.bot:
        return
    
    # Check if message has image attachments
    if message.attachments and any(att.content_type and att.content_type.startswith('image/') for att in message.attachments):
        try:
            # Add vouch point
            new_points = add_vouch_point(message.author.id)
            
            # Create thank you embed
            embed = discord.Embed(
                title="Thanks for vouching!",
                description=f"+1 point (current points: {new_points})",
                color=discord.Color.from_rgb(87, 242, 135)  # QuickEats Green
            )
            embed.set_footer(text="QuickEats Vouch System")
            
            # Reply to the message
            await message.reply(embed=embed)
            
            logger.info(f"Added vouch point to {message.author} (ID: {message.author.id}). New total: {new_points}")
            
        except Exception as e:
            logger.error(f"Error processing vouch: {e}")
            logger.error(traceback.format_exc())
```

### 4.2 Integration with Main Message Handler

```python
@bot.event
async def on_message(message):
    # Skip messages from the bot itself
    if message.author == bot.user:
        return
    
    # Handle vouch tracking
    await on_message_vouch_tracking(message)
    
    # ... rest of existing message handling logic

## 5. Slash Commands

### 5.1 User Commands

#### `/points` Command
```python
@bot.tree.command(
    name="points",
    description="Check your current vouch points",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
async def points_command(interaction: discord.Interaction):
    """
    Display user's current vouch points in a simplified embed.

    Features:
    - Shows current point total only
    - No progress tracking or role references
    - Ephemeral response (private to user)
    - QuickEats branded styling

    Embed Structure:
    - Title: "🏆 Your Vouch Points"
    - Description: "You currently have **X** vouch points!"
    - Color: QuickEats Green (87, 242, 135)
    - Footer: "QuickEats Vouch System"

    Error Handling:
    - Catches all exceptions
    - Sends error message with cancel emoji
    - Logs error details for debugging
    """
    try:
        user_points = get_user_points(interaction.user.id)

        embed = discord.Embed(
            title="🏆 Your Vouch Points",
            description=f"You currently have **{user_points}** vouch points!",
            color=discord.Color.from_rgb(87, 242, 135)  # QuickEats Green
        )

        embed.set_footer(text="QuickEats Vouch System")
        await interaction.response.send_message(embed=embed, ephemeral=True)

    except Exception as e:
        logger.error(f"Error in points command: {e}")
        logger.error(traceback.format_exc())
        await interaction.response.send_message(
            f"<:cancel:1396231869980348467> An error occurred while checking your points.",
            ephemeral=True
        )
```

#### `/leaderboard` Command
```python
@bot.tree.command(
    name="leaderboard",
    description="View the top 10 users with most vouch points",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
async def leaderboard_command(interaction: discord.Interaction):
    """
    Display top 10 users with most vouch points.

    Features:
    - Ephemeral response (private to user)
    - Medal emojis for top 3 positions (🥇🥈🥉)
    - Handles missing users gracefully
    - Shows "No data" message if empty

    Display Format:
    🥇 **Username** - 15 points
    🥈 **Username** - 12 points
    🥉 **Username** - 8 points
    4. **Username** - 5 points

    Error Handling:
    - Gracefully handles deleted/missing users
    - Continues processing if individual user lookup fails
    - Shows fallback "User {id}" if username unavailable
    """
    try:
        leaderboard = get_leaderboard()

        embed = discord.Embed(
            title="🏆 Vouch Points Leaderboard",
            description="Top 10 users with most vouch points:",
            color=discord.Color.from_rgb(87, 242, 135)  # QuickEats Green
        )

        if not leaderboard:
            embed.add_field(
                name="No Data",
                value="No vouch points recorded yet!",
                inline=False
            )
        else:
            leaderboard_text = ""
            for i, (user_id, user_data) in enumerate(leaderboard, 1):
                try:
                    user = bot.get_user(int(user_id))
                    username = user.display_name if user else f"User {user_id}"
                    points = user_data['points']

                    # Add medal emojis for top 3
                    if i == 1:
                        medal = "🥇"
                    elif i == 2:
                        medal = "🥈"
                    elif i == 3:
                        medal = "🥉"
                    else:
                        medal = f"{i}."

                    leaderboard_text += f"{medal} **{username}** - {points} points\n"

                except Exception as e:
                    logger.error(f"Error processing leaderboard entry {user_id}: {e}")
                    continue

            embed.add_field(
                name="Rankings",
                value=leaderboard_text or "No valid entries found",
                inline=False
            )

        embed.set_footer(text="QuickEats Vouch System")
        await interaction.response.send_message(embed=embed, ephemeral=True)

    except Exception as e:
        logger.error(f"Error in leaderboard command: {e}")
        logger.error(traceback.format_exc())
        await interaction.response.send_message(
            f"<:cancel:1396231869980348467> An error occurred while loading the leaderboard.",
            ephemeral=True
        )
```

### 5.2 Admin Commands

#### Role-Based Permission System
```python
# Admin role IDs that can use admin commands
ADMIN_ROLE_IDS = [1382474180028661922, 1382474181416976436, 1382474176803115110]

def check_admin_permissions(interaction: discord.Interaction) -> bool:
    """
    Check if user has required admin roles.

    Args:
        interaction: Discord interaction object

    Returns:
        bool: True if user has any of the required admin roles
    """
    user_role_ids = [role.id for role in interaction.user.roles]
    return any(role_id in user_role_ids for role_id in ADMIN_ROLE_IDS)
```

#### `/addpoints` Command
```python
@bot.tree.command(
    name="addpoints",
    description="[ADMIN] Add vouch points to a user",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(
    user="The user to add points to",
    amount="Number of points to add"
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
async def addpoints_command(interaction: discord.Interaction, user: discord.Member, amount: int):
    """
    Admin command to add points to a user.

    Permission Check:
    - Validates user has one of the required admin role IDs
    - Returns permission denied message if unauthorized

    Validation:
    - Amount must be positive (> 0)
    - Returns error message for invalid amounts

    Confirmation Embed:
    - Shows points added and new total
    - Includes admin attribution
    - Uses QuickEats green color

    Logging:
    - Records admin action with details
    - Includes admin user, target user, amount, and new total
    """
    try:
        # Check if user has required admin roles
        admin_role_ids = [1382474180028661922, 1382474181416976436, 1382474176803115110]
        user_role_ids = [role.id for role in interaction.user.roles]

        if not any(role_id in user_role_ids for role_id in admin_role_ids):
            await interaction.response.send_message(
                "<:cancel:1396231869980348467> You don't have permission to use this command!",
                ephemeral=True
            )
            return

        if amount <= 0:
            await interaction.response.send_message(
                "<:cancel:1396231869980348467> Amount must be positive!",
                ephemeral=True
            )
            return

        new_points = modify_user_points(user.id, amount)

        embed = discord.Embed(
            title="✅ Points Added",
            description=f"Added **{amount}** points to {user.mention}",
            color=discord.Color.from_rgb(87, 242, 135)  # QuickEats Green
        )
        embed.add_field(
            name="New Total",
            value=f"{new_points} points",
            inline=True
        )
        embed.add_field(
            name="Admin",
            value=interaction.user.mention,
            inline=True
        )
        embed.set_footer(text="QuickEats Vouch System")

        await interaction.response.send_message(embed=embed)
        logger.info(f"Admin {interaction.user} added {amount} points to {user} (ID: {user.id}). New total: {new_points}")

    except Exception as e:
        logger.error(f"Error in addpoints command: {e}")
        logger.error(traceback.format_exc())
        await interaction.response.send_message(
            f"<:cancel:1396231869980348467> An error occurred while adding points.",
            ephemeral=True
        )
```

#### `/removepoints` Command
```python
@bot.tree.command(
    name="removepoints",
    description="[ADMIN] Remove vouch points from a user",
    guild=None if COMMANDS_GLOBAL else GUILD
)
@app_commands.describe(
    user="The user to remove points from",
    amount="Number of points to remove"
)
@app_commands.default_permissions(send_messages=True, use_application_commands=True)
async def removepoints_command(interaction: discord.Interaction, user: discord.Member, amount: int):
    """
    Admin command to remove points from a user.

    Permission Check:
    - Same role validation as addpoints command

    Validation:
    - Amount must be positive (> 0)
    - Checks if user has points to remove
    - Returns appropriate error messages

    Point Removal:
    - Points cannot go below 0 (enforced by modify_user_points)
    - Shows actual amount removed (may be less than requested)

    Confirmation:
    - Shows actual points removed and new total
    - Handles cases where fewer points removed than requested
    """
    try:
        # Check if user has required admin roles
        admin_role_ids = [1382474180028661922, 1382474181416976436, 1382474176803115110]
        user_role_ids = [role.id for role in interaction.user.roles]

        if not any(role_id in user_role_ids for role_id in admin_role_ids):
            await interaction.response.send_message(
                "<:cancel:1396231869980348467> You don't have permission to use this command!",
                ephemeral=True
            )
            return

        if amount <= 0:
            await interaction.response.send_message(
                "<:cancel:1396231869980348467> Amount must be positive!",
                ephemeral=True
            )
            return

        current_points = get_user_points(user.id)
        if current_points == 0:
            await interaction.response.send_message(
                f"<:cancel:1396231869980348467> {user.mention} has no points to remove!",
                ephemeral=True
            )
            return

        new_points = modify_user_points(user.id, -amount)
        actual_removed = current_points - new_points

        embed = discord.Embed(
            title="✅ Points Removed",
            description=f"Removed **{actual_removed}** points from {user.mention}",
            color=discord.Color.from_rgb(87, 242, 135)  # QuickEats Green
        )
        embed.add_field(
            name="New Total",
            value=f"{new_points} points",
            inline=True
        )
        embed.add_field(
            name="Admin",
            value=interaction.user.mention,
            inline=True
        )
        embed.set_footer(text="QuickEats Vouch System")

        await interaction.response.send_message(embed=embed)
        logger.info(f"Admin {interaction.user} removed {actual_removed} points from {user} (ID: {user.id}). New total: {new_points}")

    except Exception as e:
        logger.error(f"Error in removepoints command: {e}")
        logger.error(traceback.format_exc())
        await interaction.response.send_message(
            f"<:cancel:1396231869980348467> An error occurred while removing points.",
            ephemeral=True
        )
```

## 6. Configuration

### 6.1 Channel Configuration
```python
# Vouch tracking configuration
VOUCH_CHANNEL_ID = 1382474243765440698  # Channel to monitor for image vouches
```

### 6.2 File Paths
```python
from pathlib import Path
VOUCH_DATA_FILE = Path("vouch_data.json")  # JSON database file
```

### 6.3 Admin Role IDs
```python
# Role IDs that can use admin commands (/addpoints, /removepoints)
ADMIN_ROLE_IDS = [
    1382474180028661922,  # Admin Role 1
    1382474181416976436,  # Admin Role 2
    1382474176803115110   # Admin Role 3
]
```

### 6.4 Styling Configuration
```python
# QuickEats brand color for embeds
QUICKEATS_GREEN = discord.Color.from_rgb(87, 242, 135)

# Custom emojis
CANCEL_EMOJI = "<:cancel:1396231869980348467>"
```

## 7. Dependencies

### 7.1 Required Imports
```python
import discord
from discord.ext import commands
from discord import app_commands
import json
import datetime
import traceback
import logging
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
```

### 7.2 Discord.py Features Used
- **Event Handlers**: `@bot.event` for message monitoring
- **Slash Commands**: `@bot.tree.command` for user interactions
- **App Commands**: `@app_commands.describe` for parameter descriptions
- **Permissions**: `@app_commands.default_permissions` for command access
- **Embeds**: `discord.Embed` for rich message formatting
- **Interactions**: `discord.Interaction` for slash command responses
- **Members**: `discord.Member` for user targeting in admin commands

### 7.3 Python Standard Library
- **json**: For data serialization/deserialization
- **datetime**: For timestamp generation
- **pathlib**: For file path handling
- **traceback**: For error logging
- **logging**: For system logging

## 8. Integration Points

### 8.1 Main Bot Integration
The vouch tracking system integrates with the main bot through the message event handler:

```python
@bot.event
async def on_message(message):
    # Skip messages from the bot itself
    if message.author == bot.user:
        return

    # Handle vouch tracking (NEW INTEGRATION POINT)
    await on_message_vouch_tracking(message)

    # ... existing message processing logic
    # (group order processing, etc.)
```

### 8.2 Command Registration
All slash commands are automatically registered with the Discord API through the `@bot.tree.command` decorators. No additional registration code is required.

### 8.3 Error Handling Integration
The system uses the same error handling patterns as the main bot:
- Consistent emoji usage (`<:cancel:1396231869980348467>`)
- Standardized logging with `logger.error()`
- Graceful degradation for non-critical failures

### 8.4 Styling Integration
- Uses QuickEats brand colors and styling
- Consistent embed formatting with main bot
- Same footer text pattern ("QuickEats Vouch System")

## 9. Implementation Checklist

To implement this system in a new Discord bot:

### 9.1 Core Setup
- [ ] Add all required imports
- [ ] Define configuration constants (channel ID, role IDs, file path)
- [ ] Implement all core functions (load_vouch_data, save_vouch_data, etc.)

### 9.2 Event Handler
- [ ] Implement on_message_vouch_tracking function
- [ ] Integrate with main on_message event handler
- [ ] Test image attachment detection

### 9.3 Slash Commands
- [ ] Implement /points command with simplified embed
- [ ] Implement /leaderboard command with ephemeral response
- [ ] Implement /addpoints command with role-based permissions
- [ ] Implement /removepoints command with role-based permissions

### 9.4 Testing
- [ ] Test automatic point awarding for image attachments
- [ ] Test all slash commands with various user scenarios
- [ ] Test admin permission validation
- [ ] Test data persistence across bot restarts
- [ ] Test error handling for edge cases

### 9.5 Configuration
- [ ] Update channel ID for target vouch channel
- [ ] Update admin role IDs for your server
- [ ] Update embed colors and branding as needed
- [ ] Update custom emoji IDs if different

This specification provides a complete blueprint for implementing an identical vouch tracking system in any Discord bot project using discord.py.

## 10. Additional Notes

### 10.1 Performance Considerations
- JSON file I/O occurs on every point modification
- For high-volume servers, consider database alternatives (SQLite, PostgreSQL)
- File locking may be needed for concurrent access scenarios

### 10.2 Security Considerations
- Admin role IDs are hardcoded - ensure they're kept secure
- File permissions should restrict access to vouch_data.json
- Consider rate limiting for admin commands to prevent abuse

### 10.3 Scalability Notes
- Current system handles moderate server sizes well
- For large servers (1000+ active users), consider:
  - Database migration from JSON
  - Caching frequently accessed data
  - Batch processing for leaderboard generation

### 10.4 Maintenance Tasks
- Regular backup of vouch_data.json file
- Monitor file size growth over time
- Periodic cleanup of old vouch entries if needed
- Log rotation for system logs
```
