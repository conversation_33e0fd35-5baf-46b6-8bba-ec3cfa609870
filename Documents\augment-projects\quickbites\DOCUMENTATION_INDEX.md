# QuickBites Discord Bot - Comprehensive Documentation Index

## Table of Contents
1. [Project Overview](#project-overview)
2. [Module Structure](#module-structure)
3. [Bot Features](#bot-features)
4. [Embed Templates](#embed-templates)
5. [API Integrations](#api-integrations)
6. [Configuration](#configuration)
7. [File Organization](#file-organization)

---

## Project Overview

**QuickBites** (also known as "The Method Discord Bot") is a comprehensive Discord bot designed for **Uber Eats order management and processing**. The bot serves as an intermediary service that:

- **Processes Uber Eats group orders** with automated pricing analysis
- **Applies Method discounts** ($25 USD / $32.50 CAD) to reduce customer costs
- **Handles payment processing** through multiple payment providers (Cerv, Nelo, Glitchyz)
- **Tracks order status** in real-time with automated updates
- **Manages Discord ticket system** for order fulfillment workflow
- **Provides pricing transparency** with detailed fee breakdowns
- **Supports multi-currency operations** (USD and CAD)

### Core Value Proposition
The bot enables users to order from Uber Eats at discounted rates by leveraging bulk ordering strategies and automated processing, while providing a seamless Discord-based interface for order management.

---

## Module Structure

### Main Bot Module
**File:** `themethodbot/themethodbot.py` (1,590+ lines)
- **Primary Functions:**
  - `run_bot()` - Main bot entry point and event loop
  - `setup_hook()` - Initialize HTTP sessions, load data, register views
  - `on_message()` - Process Uber Eats group order links
  - `on_ready()` - Bot startup and command synchronization
- **Key Classes:**
  - `commands.Bot` - Main Discord bot instance with optimized settings
- **Dependencies:** discord.py, aiohttp, dotenv, psutil
- **Functionality:** Central bot orchestration, command registration, event handling

### Core Bot Logic
**File:** `themethodbot/common/bot.py` (2,300+ lines)
- **Primary Functions:**
  - `process_cart_items()` - Extract and process cart items from group orders
  - `calculate_fees()` - Calculate Method pricing with discounts and fees
  - `fetch_order_details()` - Retrieve order information from Uber Eats API
  - `track_order_status()` - Real-time order tracking with status updates
  - `open_store()` / `close_store()` - Store operation management
  - `vouchtop()` - Vouch leaderboard system
  - `cerv()`, `nelo()`, `Glitchyz()` - Payment method handlers
- **Key Classes:**
  - `OrderHelpButtons` - Discord UI components for order assistance
  - `CervPaymentButtons` - Payment method selection interface
- **Dependencies:** aiohttp, beautifulsoup4, json, asyncio
- **Functionality:** Core business logic, API interactions, order processing

### Advanced Price Analysis
**File:** `themethodbot/pricecheckerv2.py` (800+ lines)
- **Primary Functions:**
  - `method_bot_price_analysis()` - Comprehensive pricing analysis
  - `add_group_order_to_personal_cart()` - Cart recreation for accurate pricing
  - `extract_cart_info()` - Extract cart details from existing orders
  - `check_group_order_pricing()` - Primary pricing interface
- **Key Features:**
  - Personal cart integration for real-time pricing
  - Multi-currency support (USD/CAD)
  - Uber One discount detection
  - Two-step cart creation process
- **Dependencies:** aiohttp, json, logging
- **Functionality:** Enhanced pricing accuracy, personal account integration

### Group Order Processing
**File:** `themethodbot/common/check_group_order.py` (750+ lines)
- **Primary Functions:**
  - `process_group_order()` - Main group order processing pipeline
  - `get_order_details_from_data()` - Extract order details from API responses
  - `validate_session()` - Verify Uber Eats session validity
  - `make_api_request()` - Standardized API request handler
- **Key Features:**
  - Group order validation and processing
  - Cart item extraction and formatting
  - Location and store information parsing
- **Dependencies:** aiohttp, json, logging
- **Functionality:** Group order validation, data extraction, API communication

### Payment Processing
**File:** `themethodbot/paymentapp.py` (100+ lines)
- **Primary Functions:**
  - `setup_payment_views()` - Register persistent payment UI components
- **Key Classes:**
  - `PaymentMethodButtons` - Main payment method selection interface
  - `PaymentMethodSelector` - Payment provider selection system
- **Dependencies:** discord.py, logging
- **Functionality:** Payment method selection, UI component management

### Fee Calculation Engine
**File:** `themethodbot/fee_calculator.py` (56 lines)
- **Primary Functions:**
  - `calculate_fees_without_delivery()` - Calculate all fees excluding delivery
- **Key Features:**
  - Multi-currency fee calculation (USD/CAD)
  - Fixed discount application ($25 USD / $32.50 CAD)
  - Method fee integration ($10 USD / $13 CAD)
  - Overflow fee calculation
- **Dependencies:** logging, typing
- **Functionality:** Standardized fee calculation, pricing transparency

### Discord Embed Templates
**File:** `themethodbot/embed_templates.py` (217 lines)
- **Primary Functions:**
  - `create_order_summary_embed()` - Comprehensive order summary with pricing
  - `create_locked_order_embed()` - Locked/canceled order guidance
  - `create_error_embed()` - Error message formatting
  - `create_processing_embed()` - Processing status indicator
- **Dependencies:** discord.py, logging, typing
- **Functionality:** Consistent Discord message formatting, visual branding

### Utility Modules
**File:** `themethodbot/common/extract_label.py`
- **Functionality:** Label extraction from order data

**File:** `themethodbot/common/config.py` (30 lines)
- **Primary Functions:**
  - `configure_logging()` - Dynamic logging configuration
- **Functionality:** Debug mode management, logging setup

---

## Bot Features

### Slash Commands

#### Order Management Commands
- **`/track <order_id>`**
  - **Description:** Track an Uber order status with real-time updates
  - **Permissions:** Default user permissions
  - **Parameters:** `order_id` (string) - The Uber order ID to track
  - **Response:** Ephemeral tracking confirmation with status embed

#### Payment Method Commands
- **`/cerv`**
  - **Description:** Display Cerv's payment details and options
  - **Permissions:** Default user permissions
  - **Response:** Interactive payment method selection

- **`/nelo`**
  - **Description:** Display Nelo's payment details and options
  - **Permissions:** Default user permissions
  - **Response:** Payment information embed

- **`/glitchyz`**
  - **Description:** Display Glitchyz's payment details and options
  - **Permissions:** Default user permissions
  - **Response:** Payment method interface

#### Store Management Commands (Staff Only)
- **`/open`**
  - **Description:** Opens the store for order processing
  - **Permissions:** Role ID `1340830489719734443` (Staff)
  - **Response:** Store status update with help buttons

- **`/close`**
  - **Description:** Closes the store and stops order processing
  - **Permissions:** Role ID `1340830489719734443` (Staff)
  - **Response:** Store closure confirmation

#### Utility Commands
- **`/vouchtop`**
  - **Description:** Display the vouch leaderboard
  - **Permissions:** Default user permissions
  - **Response:** Leaderboard embed with user rankings

- **`/sync`** (Admin Only)
  - **Description:** Manually sync slash commands
  - **Permissions:** Administrator
  - **Response:** Command synchronization status

#### Staff Management Commands
- **`/cleardelivering`**
  - **Description:** Close all channels with 'delivering' in the name
  - **Permissions:** Role ID `1340213865748762634` (Staff)
  - **Cooldown:** 30 seconds per user
  - **Response:** Bulk channel closure confirmation

### Interactive Features

#### Automatic Order Processing
- **Trigger:** Uber Eats group order links posted in ticket channels
- **Process:**
  1. Extract group order UUID and cart UUID from URL
  2. Fetch order details via Uber Eats API
  3. Apply Method pricing analysis
  4. Generate comprehensive order summary embed
  5. Move channel to queue if subtotal meets criteria ($23-$35)

#### Real-Time Order Tracking
- **Trigger:** `/track` command or automatic tracking initiation
- **Features:**
  - Persistent status monitoring
  - Discord timestamp formatting
  - Status history tracking
  - Automatic embed updates

#### Payment Method Selection
- **Interactive Buttons:** Cerv, Nelo, Glitchyz payment options
- **Persistent Views:** Survive bot restarts
- **Dynamic Content:** Context-aware payment information

#### Queue Management System
- **Categories:**
  - **Tickets:** `1340194718637625395` - New order submissions
  - **Queue:** `1389060752832200745` - Validated orders awaiting processing
  - **Delivering:** `1354242418211422419` - Orders in delivery status
- **Validation:** Subtotal range checking ($23-$35)
- **Automation:** Channel movement based on order status

---

## Embed Templates

### Order Summary Embed
- **Template Name:** `create_order_summary_embed()`
- **Trigger Conditions:** Successful group order processing
- **Visual Elements:**
  - **Title:** `<:burger:1360153508929732617> The Method Order Summary (BETA)`
  - **Color:** Vibrant Green (`87, 242, 135`)
  - **Thumbnail:** Animated food icon
- **Fields:**
  - **Group Order Link:** Clickable link to Uber Eats group order
  - **Delivery Location:** Full address with city, state, ZIP
  - **Restaurant:** Link to restaurant on Uber Eats
  - **Order Items:** List of cart items (max 10 displayed)
  - **Pricing Breakdown:** Original subtotal, Uber discount, Method fee
  - **Your Total (BETA):** Final calculated price
- **Data Sources:** API response, pricing analysis, cart items
- **Use Cases:** Order confirmation, pricing transparency, customer communication

### Locked Order Embed
- **Template Name:** `create_locked_order_embed()`
- **Trigger Conditions:** Group order is locked or canceled
- **Visual Elements:**
  - **Title:** `<:padlock:1360153078149677207> Group Order is Locked`
  - **Color:** Discord Red (`237, 66, 69`)
  - **Thumbnail:** Animated padlock icon
- **Fields:**
  - **Step 1:** Go back to Uber Eats Cart
  - **Step 2:** Press Unlock Group Order
  - **Step 3:** Resend cart link to channel
- **Use Cases:** Error recovery, user guidance, troubleshooting

### Error Embed
- **Template Name:** `create_error_embed()`
- **Trigger Conditions:** Order processing failures, API errors
- **Visual Elements:**
  - **Title:** `<:cancel:1360154555295207596> Error Processing Order`
  - **Color:** Discord Red (`237, 66, 69`)
  - **Thumbnail:** Animated alert icon
- **Fields:**
  - **Error Message:** Code-formatted error details
  - **Troubleshooting Steps:** User-friendly resolution guidance
- **Footer:** Support contact information
- **Use Cases:** Error communication, debugging assistance, user support

### Processing Embed
- **Template Name:** `create_processing_embed()`
- **Trigger Conditions:** Order analysis in progress
- **Visual Elements:**
  - **Title:** `🔄 Processing Your Order`
  - **Color:** Discord Blurple (`88, 101, 242`)
  - **Thumbnail:** Animated loading spinner
- **Description:** "We're analyzing your Uber Eats group order. This may take a few seconds..."
- **Use Cases:** Loading states, user feedback, processing indication

### Success Processing Embed
- **Template Name:** Inline creation in `on_message()`
- **Trigger Conditions:** Successful order processing completion
- **Visual Elements:**
  - **Title:** `<:check:1360153501866393692> Order Processed Successfully`
  - **Color:** Vibrant Green (`87, 242, 135`)
- **Fields:**
  - **Pricing Analysis:** Original price → Method price comparison
  - **Next Steps:** Instructions for customer to wait for assistance
- **Use Cases:** Success confirmation, pricing summary, workflow guidance

---

## API Integrations

### Uber Eats API Integration
- **Base URL:** `https://www.ubereats.com/_p/api/`
- **Authentication:** Cookie-based session authentication
- **Key Endpoints:**
  - **`getDraftOrderByUuidV2`** - Retrieve group order details
  - **`getCheckoutPresentationV1`** - Get pricing and cart information
  - **`getActiveOrdersV1`** - Session validation and active order checking
  - **`createDraftOrderV2`** - Create new draft orders for pricing analysis
  - **`addItemsToDraftOrderV2`** - Add items to existing draft orders
- **Rate Limiting:** 10-second timeout per request
- **Error Handling:** Comprehensive retry logic and fallback mechanisms
- **Data Format:** JSON payloads with UUID-based identification

### HERE Maps API Integration
- **Purpose:** Address validation and geocoding
- **Authentication:** API key-based (`HERE_API_KEY`)
- **Use Cases:** Location verification, address standardization

### Stripe Payment Processing
- **Integration Type:** Webhook-based payment processing
- **Authentication:**
  - **Secret Key:** `STRIPE_SECRET_KEY`
  - **Webhook Secret:** `STRIPE_WEBHOOK_SECRET`
- **Use Cases:** Payment verification, transaction processing

### Discord API Integration
- **Framework:** discord.py 2.0+
- **Features:**
  - **Slash Commands:** Application command framework
  - **Persistent Views:** UI components that survive restarts
  - **Webhooks:** External notification system
  - **Message Components:** Interactive buttons and select menus
- **Permissions:** Granular role-based access control

### Email Integration
- **Provider:** Gmail SMTP
- **Authentication:** App-specific password
- **Use Cases:** Notification system, order confirmations

---

## Configuration

### Required Environment Variables

#### Discord Configuration
```env
DISCORD_BOT_TOKEN=<Main bot token for The Method Bot>
DISCORD_GUILD_ID=<Primary Discord server ID: 1340194718637625394>
TOKEN_2=<Secondary bot token for channel operations>
EATSGUILD_ID=<Secondary guild ID: 1278357079542726736>
DISCORD_WEBHOOK_URL=<Webhook URL for external notifications>
```

#### Uber Eats Integration
```env
UBER_COOKIE=<Uber Eats session cookie for API authentication>
```

#### External API Keys
```env
HERE_API_KEY=<HERE Maps API key for address validation>
STRIPE_SECRET_KEY=<Stripe secret key for payment processing>
STRIPE_WEBHOOK_SECRET=<Stripe webhook secret for verification>
```

#### Email Configuration
```env
EMAIL_ADDRESS=<Gmail address for notifications>
EMAIL_PASSWORD=<Gmail app-specific password>
```

#### Additional Bot Tokens
```env
checker_token=<Token for price checking bot>
EATSchecker_token=<Token for Eats checking bot>
BUDGET_DISCORD_BOT_TOKEN=<Token for budget tracking bot>
BUDGET_DISCORD_GUILD_ID=<Guild ID for budget bot>
```

### Configuration Constants

#### Channel and Category IDs
```python
TICKET_CATEGORY_ID = 1340194718637625395    # New ticket submissions
QUEUE_CATEGORY_ID = 1389060752832200745     # Validated orders queue
DELIVERING_CATEGORY_ID = 1354242418211422419 # Orders in delivery
CREDITS_LOG_CHANNEL_ID = 1389728392601010300 # Credits system logging
STATUS_MESSAGE_ID = 1365395438542262345     # Status message for updates
```

#### Order Validation Limits
```python
MIN_SUBTOTAL = 23.0  # Minimum subtotal for queue eligibility
MAX_SUBTOTAL = 35.0  # Maximum subtotal for queue eligibility
```

#### Pricing Configuration
```python
# USD Configuration
USD_DISCOUNT = 25.00      # Method discount amount
USD_METHOD_FEE = 10.00    # Method processing fee
USD_OVERFLOW_THRESHOLD = 20.00  # Overflow fee threshold

# CAD Configuration (30% higher)
CAD_DISCOUNT = 32.50      # Method discount amount
CAD_METHOD_FEE = 13.00    # Method processing fee
CAD_OVERFLOW_THRESHOLD = 30.00  # Overflow fee threshold
```

### Data Storage Files

#### Persistent Data Files
- **`tracking_data.json`** - Active order tracking information
- **`status_embed_data.json`** - Status message embed data
- **`credits_data.json`** - User credit balances
- **`redeemed_keys.json`** - Redeemed serial key tracking

#### Log Files
- **`themethodbot.log`** - Main bot activity log
- **`debug.log`** - Debug-level logging output

### Dependencies (requirements.txt)
```
discord.py>=2.0.0          # Discord API framework
python-dotenv>=0.19.0      # Environment variable management
aiohttp>=3.8.0             # Async HTTP client for API requests
psutil>=5.9.0              # System monitoring and metrics
requests>=2.27.0           # HTTP requests library
beautifulsoup4>=4.10.0     # HTML parsing for web scraping
```

---

## File Organization

### Project Root Structure
```
quickbites/
├── main.py                          # Alternative entry point
├── run_all_bots.py                  # Multi-bot launcher
├── requirements.txt                 # Python dependencies
├── README.md                        # Project documentation
├── .env                            # Environment variables
├── themethodbot.log                # Main log file
├── tracking_data.json              # Order tracking data
├── status_embed_data.json          # Status embed data
├── credits_data.json               # User credits data
├── redeemed_keys.json              # Serial key redemption data
└── themethodbot/                   # Main bot package
    ├── __init__.py                 # Package initialization
    ├── themethodbot.py             # Main bot implementation (PRIMARY ENTRY)
    ├── pricecheckerv2.py           # Advanced pricing analysis
    ├── paymentapp.py               # Payment processing interface
    ├── embed_templates.py          # Discord embed templates
    ├── fee_calculator.py           # Fee calculation engine
    └── common/                     # Shared utilities
        ├── __init__.py             # Package initialization
        ├── bot.py                  # Core bot functionality
        ├── check_group_order.py    # Group order processing
        ├── extract_label.py        # Label extraction utilities
        └── config.py               # Configuration management
```

### Module Relationships

#### Core Dependencies
```
themethodbot.py (Main Entry Point)
├── imports from common/bot.py (Core Functions)
├── imports from pricecheckerv2.py (Pricing Analysis)
├── imports from paymentapp.py (Payment Processing)
├── imports from embed_templates.py (Discord Embeds)
└── imports from common/check_group_order.py (Order Processing)

common/bot.py (Core Logic)
├── imports from fee_calculator.py (Fee Calculations)
├── imports from common/config.py (Configuration)
└── imports from common/extract_label.py (Utilities)
```

#### Data Flow Architecture
```
Discord Message (Uber Eats Link)
    ↓
themethodbot.py (on_message event)
    ↓
common/check_group_order.py (process_group_order)
    ↓
pricecheckerv2.py (method_bot_price_analysis)
    ↓
fee_calculator.py (calculate_fees_without_delivery)
    ↓
embed_templates.py (create_order_summary_embed)
    ↓
Discord Channel (Order Summary Response)
```

### Execution Entry Points

#### Primary Entry Point
- **File:** `themethodbot/themethodbot.py`
- **Function:** `run_bot()`
- **Usage:** Direct bot execution with full feature set

#### Alternative Entry Points
- **File:** `main.py` - Wrapper for themethodbot execution
- **File:** `run_all_bots.py` - Multi-bot launcher for development

#### Batch/Shell Scripts
- **Windows:** `run_all_bots.bat`
- **Linux/Mac:** `run_all_bots.sh`

---

## Development Notes

### Code Quality Standards
- **Logging:** Comprehensive logging with structured messages
- **Error Handling:** Try-catch blocks with detailed error reporting
- **Type Hints:** Extensive use of typing module for code clarity
- **Documentation:** Inline comments and docstrings for complex functions

### Performance Optimizations
- **HTTP Session Reuse:** Global aiohttp session for API efficiency
- **Message Caching:** 10,000 message cache for improved performance
- **Heartbeat Timeout:** Extended timeout (150s) for connection stability
- **Async Operations:** Non-blocking I/O for concurrent request handling

### Security Considerations
- **Environment Variables:** Sensitive data stored in .env files
- **Role-Based Permissions:** Discord role verification for admin commands
- **API Rate Limiting:** Request throttling to prevent API abuse
- **Session Validation:** Regular validation of Uber Eats authentication

---

*This documentation index serves as a comprehensive reference for developers working with the QuickBites Discord bot. For specific implementation details, refer to the individual module files and their inline documentation.*
