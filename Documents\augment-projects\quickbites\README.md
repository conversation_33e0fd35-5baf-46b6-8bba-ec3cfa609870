# QuickBites Discord Bot

This repository contains the QuickBites Discord bot used for order management and processing.

## Project Structure

The project is organized as follows:

- `quickbitesbot/` - The main Discord bot for order management
  - `quickbitesbot.py` - Main bot implementation (primary entry point)
  - `common/` - Shared code and utilities used by the bot
  - `paymentapp.py` - Payment processing functionality
  - `embed_templates.py` - Discord embed templates
  - `fee_calculator.py` - Fee calculation logic

## Running the Bot

You can run the QuickBites Bot directly using any of these methods:

### Method 1: Direct Execution (Recommended)
```bash
# Run the QuickBites Bot directly
python quickbitesbot/quickbitesbot.py
```

### Method 2: From the quickbitesbot directory
```bash
# Navigate to the quickbitesbot directory and run
cd quickbitesbot
python quickbitesbot.py
```

### Method 3: Using the run_all_bots script
```bash
python run_all_bots.py
```

### Method 4: Using batch/shell scripts
- **Windows**: Double-click the `run_all_bots.bat` file
- **Linux/Mac**: Run `./run_all_bots.sh`

## Dependencies

The bot dependencies are listed in the requirements.txt file.

## Configuration

The bot uses environment variables for configuration. Make sure to set up the appropriate .env file with the required variables.

## Common Code

The `quickbitesbot/common/` directory contains shared code and utilities:

- `bot.py` - Core bot functionality and Discord commands
- `check_group_order.py` - Functions for checking Uber Eats group orders
- `extract_label.py` - Functions for extracting labels from orders
- `config.py` - Configuration and logging setup
- `latestsummary.py` - Latest summary functionality
