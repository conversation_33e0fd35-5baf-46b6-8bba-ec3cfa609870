import logging
from typing import Dict, Any

logger = logging.getLogger('quickbitesbot.fee_calculator')

def calculate_fees_without_delivery(fees_data: Dict[str, Any], subtotal: float, is_cad: bool = False) -> Dict[str, Any]:
    """Calculate all fee-related values, excluding delivery fee."""
    # Always use the provided subtotal parameter
    actual_subtotal = subtotal

    # Set fixed discount amount based on region
    fixed_discount = 32.50 if is_cad else 25.00  # CAD gets $32.50 discount (30% higher than USD $25)

    # Calculate discounted subtotal and savings
    discounted_subtotal = round(max(0, actual_subtotal - fixed_discount), 2)  # Ensure non-negative
    savings = round(actual_subtotal - discounted_subtotal, 2)

    # Calculate overflow fee
    threshold = 30 if is_cad else 20
    overflow_fee = round(max(0, actual_subtotal - threshold), 2)

    # Extract fees from fees_data
    service_fee = float(fees_data.get('service_fee', 0))
    ca_driver_benefit = float(fees_data.get('ca_driver_benefit', 0))
    taxes = float(fees_data.get('taxes', 0))
    uber_one_discount = float(fees_data.get('uber_one_discount', 0))

    # Add QuickBites fee component - $10.00 for USD, $13.00 for CAD (30% higher)
    quickbites_fee = 13.00 if is_cad else 10.00

    # Calculate total fees (excluding delivery fee and overflow fee, but including QuickBites fee)
    total_fees = round(service_fee + ca_driver_benefit + taxes + quickbites_fee, 2)
    final_fees = round(total_fees - uber_one_discount, 2)

    # Calculate final total (excluding delivery fee)
    final_total = round(discounted_subtotal + overflow_fee + final_fees, 2)

    # Return all calculated values
    return {
        'subtotal': actual_subtotal,
        'discounted_subtotal': discounted_subtotal,
        'savings': savings,
        'fixed_discount': fixed_discount,
        'overflow_fee': overflow_fee,
        'service_fee': service_fee,
        'ca_driver_benefit': ca_driver_benefit,
        'taxes': taxes,
        'uber_one_discount': uber_one_discount,
        'quickbites_fee': quickbites_fee,
        'method_fee': quickbites_fee,  # Keep for backward compatibility
        'fixed_fee': quickbites_fee,  # Keep for backward compatibility
        'total_fees': total_fees,
        'final_fees': final_fees,
        'final_total': final_total,
        'is_cad': is_cad
    }
