"""
PriceCheckerV2 - Improved price checking system for TheMethodBot

This module implements an enhanced price checking system that extracts cart information
from existing group orders and recreates them with fresh pricing data using Uber Eats API endpoints.

INTEGRATION WITH EXISTING SYSTEM:
- Compatible with fee calculation functions in common/bot.py
- Maintains same data structure format as current price checking system
- Supports both USD and CAD currency detection and handling
- Can be used as a drop-in replacement for existing price checking logic

MAIN FUNCTIONS:
- add_group_order_to_personal_cart(): Add group order items to your personal Uber Eats cart
- extract_cart_info(): Extract cart details from existing group orders
- check_group_order_pricing(): Primary interface for comprehensive price analysis
- set_personal_authentication(): Configure personal account authentication

PERSONAL CART INTEGRATION:
The system uses a two-step process to add items to your personal cart:
1. createDraftOrderV2: Creates draft order with first item
2. addItemsToDraftOrderV2: Adds remaining items to the draft order

USAGE EXAMPLE:
    import asyncio
    from pricecheckerv2 import add_group_order_to_personal_cart

    async def main():
        result = await add_group_order_to_personal_cart(
            "https://eats.uber.com/group-orders/UUID/join"
        )
        if result['success']:
            print(f"✅ Added {result['items_added_count']} items!")
            print(f"🔗 Cart: {result['cart_url']}")

Author: TheMethodBot Development Team
Version: 2.0
"""

import asyncio
import aiohttp
import json
import logging
import re
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# =============================================================================
# DEBUG CONFIGURATION
# =============================================================================

# Phase 4 Cleanup Toggle - Set to False to disable cleanup for debugging
# When disabled, personal carts and group memberships will be preserved for inspection
ENABLE_PHASE4_CLEANUP = True

# Debug logging for Phase 4 operations
DEBUG_PHASE4_LOGGING = True

# =============================================================================
# DEBUG TOGGLE FUNCTIONS
# =============================================================================

def enable_phase4_cleanup():
    """Enable Phase 4 cleanup for production use."""
    global ENABLE_PHASE4_CLEANUP
    ENABLE_PHASE4_CLEANUP = True
    logger.info("🔧 Phase 4 cleanup ENABLED - Production mode active")

def disable_phase4_cleanup():
    """Disable Phase 4 cleanup for debugging purposes."""
    global ENABLE_PHASE4_CLEANUP
    ENABLE_PHASE4_CLEANUP = False
    logger.info("🔍 Phase 4 cleanup DISABLED - Debug mode active")

def is_phase4_cleanup_enabled():
    """Check if Phase 4 cleanup is currently enabled."""
    return ENABLE_PHASE4_CLEANUP

def get_debug_status():
    """Get current debug configuration status."""
    return {
        'phase4_cleanup_enabled': ENABLE_PHASE4_CLEANUP,
        'debug_logging_enabled': DEBUG_PHASE4_LOGGING,
        'mode': 'Production' if ENABLE_PHASE4_CLEANUP else 'Debug'
    }

# Default headers for API requests (updated with real format)
DEFAULT_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Accept": "*/*",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Content-Type": "application/json",
    "Origin": "https://www.ubereats.com",
    "Referer": "https://www.ubereats.com/",
    "Sec-Ch-Ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "Sec-Ch-Ua-Mobile": "?0",
    "Sec-Ch-Ua-Platform": '"Windows"',
    "Sec-Ch-Prefers-Color-Scheme": "dark",
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "Priority": "u=1, i",
    "X-Csrf-Token": "x",
    "X-Uber-Client-Gitref": "0d07562558a1aa2bed442b306c086ca985954d12"
}

# Personal account headers (based on captured successful request)
PERSONAL_ACCOUNT_HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Accept": "*/*",
    "Accept-Language": "en-US,en;q=0.9",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Content-Type": "application/json",
    "Origin": "https://www.ubereats.com",
    "Priority": "u=1, i",
    "Sec-Ch-Prefers-Color-Scheme": "dark",
    "Sec-Ch-Ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "Sec-Ch-Ua-Mobile": "?0",
    "Sec-Ch-Ua-Platform": '"Windows"',
    "Sec-Fetch-Dest": "empty",
    "Sec-Fetch-Mode": "cors",
    "Sec-Fetch-Site": "same-origin",
    "X-Csrf-Token": "x",
    "X-Uber-Client-Gitref": "0d07562558a1aa2bed442b306c086ca985954d12"
}

# API Base URL
API_BASE_URL = "https://www.ubereats.com/_p/api"

# Default cookie string for authentication (READ-ONLY - for group order extraction)
DEFAULT_COOKIE_STRING = """uev2.id.xp=e2426458-f85b-4e1f-bfd4-e1edf8ac1697; dId=457a9626-2f05-4d71-8a28-8c94820b005b; uev2.id.session=c6362382-1e0a-4cc5-863a-d949e6e2a23d; uev2.ts.session=1752746526978; _ua={"session_id":"1c00ecca-3ae5-4485-8d09-71f1170e7e1f","session_time_ms":1752746527005}; marketing_vistor_id=55141ee6-78be-4ea2-acec-18b219a641a1; jwt-session=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7InNsYXRlLWV4cGlyZXMtYXQiOjE3NTI3NDgzMjcwMDV9LCJpYXQiOjE3NTI3NDY1MjcsImV4cCI6MTc1MjgzMjkyN30.jUERNlVDW-WfL6W9E8b2zKl4eMYLtpAcqlOOknduw-0; uev2.embed_theme_preference=dark; utag_main__sn=1; utag_main_ses_id=1752746527630%3Bexp-session; utm_medium=undefined; utm_source=undefined; utag_main__ss=0%3Bexp-session; uev2.loc=%7B%22latitude%22%3A37.7300473%2C%22longitude%22%3A-122.4212432%2C%22address%22%3A%7B%22address1%22%3A%2258%20Stoneyford%20Ave%22%2C%22address2%22%3A%22%22%2C%22aptOrSuite%22%3A%22%22%2C%22eaterFormattedAddress%22%3A%22%22%2C%22title%22%3A%2258%20Stoneyford%20Ave%22%2C%22subtitle%22%3A%22San%20Francisco%2C%20CA%22%2C%22uuid%22%3A%22%22%7D%2C%22reference%22%3A%2214c3ec17-24ea-cadc-5395-d9465571fcbe%22%2C%22referenceType%22%3A%22uber_places%22%2C%22type%22%3A%22uber_places%22%2C%22addressComponents%22%3A%7B%22countryCode%22%3A%22%22%2C%22firstLevelSubdivisionCode%22%3A%22%22%2C%22city%22%3A%22%22%2C%22postalCode%22%3A%22%22%7D%7D; uev2.diningMode=DELIVERY; u-cookie-prefs=*******************************************************************************************************************%3D; uev2.gg=false; uev2.unregisteredUserUuid=ef290128-bfeb-5223-ad92-d62e11ece0e8; uev2.do=2588deda-d70c-4ecc-b9ec-64653d00714b; utag_main__pn=9%3Bexp-session; utag_main__se=39%3Bexp-session; utag_main__st=*************%3Bexp-session; _userUuid=undefined"""

# Personal Uber One account authentication (ACTIVE UBER ONE SUBSCRIPTION)
PERSONAL_ACCOUNT_COOKIES = """uev2.id.xp=32f9e17f-d827-495e-a660-412feb450abf; dId=936ca797-6811-46fe-9d86-6b6b09819e22; uev2.id.session=f3bcadaf-e1ca-4b46-b355-1c936a60c7c1; uev2.ts.session=*************; _ua={"session_id":"41b1a8dd-8022-4cc0-a088-c2122b268a52","session_time_ms":*************}; marketing_vistor_id=6209fa09-c3e9-47f1-8223-076b280b3180; uev2.embed_theme_preference=dark; utag_main__sn=1; utag_main_ses_id=*************%3Bexp-session; utm_medium=undefined; utm_source=undefined; utag_main__ss=0%3Bexp-session; uev2.gg=true; _scid=LLJoKZLG1xx3fXIfpnFmZDfHJKm8AbSR; _clck=1af7cn6%7C2%7Cfxp%7C0%7C2025; _tt_enable_cookie=1; _ttp=01K0EGV6S2RW8H6RTZ658CWEDQ_.tt.1; _yjsu_yjad=**********.ea730073-b96a-4b7b-9fc0-145b9c955beb; _ga=GA1.1.**********.**********; sid=QA.CAESECGFYwm9NkGMm5krBHDlnXUYo9uGxQYiATEqJGIzODI4NDY1LTZjOWUtNGFlOS05MjYyLWEwZjdmMzZkNWI4MDI84PXBvUlqpdl64oXxxKjZnMELe4bNxiJt6E84Qoj04G9t_DEgt9XrhigLEjDfh738PPMiitMyCObkr7oROgExQg0udWJlcmVhdHMuY29t.auA8EX1dkBVvIzLMvBFTMJgyBStnkw8eDuEwJT04u4g; smeta={expiresAt:1755426211950}; udi-id=Tq7Azw+RqJ7gZKMQPq3fQcXBFe0rUjjk9G+fcDN0z9RV9Fj6RfiSh9V6GuArUduGaV0vsrbKD2hB9hBZQJcglXF5nBl7QC8ljTJ4uXti6C+7PFFy9DansLJTy0P5cNMN3b3NYXSZWduZMn4waNjPfsqhzOOsl+jevRVd7151Tgd0z8qKwCWU9asWtvPSUS7lZHIETb4YYs/QBVpzm4imAA==l0jF0G9GiNU8WvgPzx0d1A==sKO98rLnAyr6OI9wH9s3WDlmZyyCmjh/avuuMnHHvl4=; _ScCbts=%5B%5D; _cc=ARdw4pxKqq9%2BGCqIdzyEF8PB; _cid_cc=ARdw4pxKqq9%2BGCqIdzyEF8PB; uev2.loc=%7B%22latitude%22%3A41.465%2C%22longitude%22%3A-74.421%2C%22address%22%3A%7B%22address1%22%3A%2223%20Kennedy%20Ter%22%2C%22address2%22%3A%22%22%2C%22aptOrSuite%22%3A%22%22%2C%22eaterFormattedAddress%22%3A%22%22%2C%22title%22%3A%2223%20Kennedy%20Ter%22%2C%22subtitle%22%3A%22Middletown%2C%20NY%22%2C%22uuid%22%3A%22%22%7D%2C%22reference%22%3A%2236e9a810-0d51-59f1-7d6b-f04f1d87cdcd%22%2C%22referenceType%22%3A%22uber_places%22%2C%22type%22%3A%22uber_places%22%2C%22addressComponents%22%3A%7B%22countryCode%22%3A%22%22%2C%22firstLevelSubdivisionCode%22%3A%22%22%2C%22city%22%3A%22%22%2C%22postalCode%22%3A%22%22%7D%2C%22originType%22%3A%22user_autocomplete%22%7D; uev2.diningMode=DELIVERY; jwt-session=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJkYXRhIjp7InNsYXRlLWV4cGlyZXMtYXQiOjE3NTI4MzgxNTczNDd9LCJpYXQiOjE3NTI4MzQwOTAsImV4cCI6MTc1MjkyMDQ5MH0.gaceq8LN4FUXgQmMHEFgWFSBxDJnp4E1vaDR6RBQilg; udi-fingerprint=J4vasd9oqXdBovdZt5oj3Vpf7wZs848glSHTr1x+Vy8XUtEOPGYolJ29TsEvRUK+nFDqzqQXMucjbwMDPi770Q==n3J0VusqzW6lZuaC7/BpM1NPctKpkWZN1lrTGWM9/0I=; utag_main__pn=7%3Bexp-session; _scid_r=OjJoKZLG1xx3fXIfpnFmZDfHJKm8AbSR; _userUuid=b3828465-6c9e-4ae9-9262-a0f7f36d5b80; _uetsid=f6d6026063c011f08f93097a2780a568; _uetvid=f6d6237063c011f0abdf5f680b61c243; uev2.do=7ce152d9-a12a-4cc9-b793-29bd27dcc0a9; u-cookie-prefs=eyJ2ZXJzaW9uIjoxMDAsImRhdGUiOjE3NTI4Mzc3ODk0NjQsImNvb2tpZUNhdGVnb3JpZXMiOlsiYWxsIl0sImltcGxpY2l0Ijp0cnVlfQ%3D%3D; utag_main__se=41%3Bexp-session; utag_main__st=1752839589465%3Bexp-session; _ga_P1RM71MPFP=GS2.1.s**********$o1$g1$t1752837789$j39$l0$h0; _gcl_au=1.1.1990686105.**********.724926190.1752834210.1752837789; ttcsid_C69TD6PO8QD6LKH42DTG=**********716::vTbK2fQSbfzZ7AORFiK3.1.1752837789902; ttcsid=**********717::Z1MkOctFBX49rIw8Sk-g.1.1752837789902"""


def parse_cookies(cookie_string: str) -> Dict[str, str]:
    """
    Parse cookie string into a dictionary format suitable for aiohttp.
    
    Args:
        cookie_string: Raw cookie string from browser
        
    Returns:
        Dictionary of cookie name-value pairs
    """
    cookies = {}
    try:
        # Split by semicolon and process each cookie
        for cookie in cookie_string.split(';'):
            cookie = cookie.strip()
            if '=' in cookie:
                name, value = cookie.split('=', 1)
                cookies[name.strip()] = value.strip()
        
        logger.info(f"Parsed {len(cookies)} cookies successfully")
        return cookies
        
    except Exception as e:
        logger.error(f"Error parsing cookies: {str(e)}")
        return {}


async def make_api_request(session: aiohttp.ClientSession, endpoint: str, payload: Dict[str, Any], use_personal_headers: bool = False) -> Dict[str, Any]:
    """
    Make an authenticated API request to Uber Eats endpoints.

    Args:
        session: Authenticated aiohttp session
        endpoint: API endpoint name (e.g., 'getCheckoutPresentationV1')
        payload: Request payload data
        use_personal_headers: Whether to use personal account headers for write operations

    Returns:
        Dictionary containing response status and data
    """
    try:
        url = f"{API_BASE_URL}/{endpoint}"
        timeout = aiohttp.ClientTimeout(total=15)

        # Choose headers based on operation type
        headers = PERSONAL_ACCOUNT_HEADERS if use_personal_headers else DEFAULT_HEADERS

        logger.debug(f"Making API request to: {url}")
        logger.debug(f"Using personal headers: {use_personal_headers}")
        logger.debug(f"Payload: {json.dumps(payload, indent=2)}")

        async with session.post(
            url,
            json=payload,
            headers=headers,
            timeout=timeout
        ) as response:
            response_data = await response.json()
            
            logger.debug(f"API Response Status: {response.status}")
            logger.debug(f"API Response: {json.dumps(response_data, indent=2)}")
            
            return {
                "status": response.status,
                "data": response_data,
                "success": response.status == 200
            }
            
    except asyncio.TimeoutError:
        logger.error(f"API request to {endpoint} timed out")
        return {
            "status": 408,
            "error": "Request timeout",
            "success": False
        }
    except Exception as e:
        logger.error(f"API request to {endpoint} failed: {str(e)}")
        return {
            "status": 500,
            "error": str(e),
            "success": False
        }


def detect_currency(pricing_data: Dict[str, Any]) -> Tuple[bool, str]:
    """
    Detect currency type from pricing data.
    
    Args:
        pricing_data: Dictionary containing pricing information
        
    Returns:
        Tuple of (is_cad: bool, currency_symbol: str)
    """
    # Check for CAD indicators in various fields
    data_str = json.dumps(pricing_data).lower()
    
    # Look for CAD currency indicators
    cad_indicators = ['ca$', 'cad', '"ca"', 'canada', 'canadian']
    is_cad = any(indicator in data_str for indicator in cad_indicators)
    
    currency_symbol = "CA$" if is_cad else "$"
    
    logger.info(f"Currency detected: {'CAD' if is_cad else 'USD'} ({currency_symbol})")
    return is_cad, currency_symbol


def extract_group_uuid_from_link(group_link: str) -> str:
    """
    Extract group order UUID from Uber Eats group order link.

    Supports multiple link formats:
    - https://www.ubereats.com/orders/group-orders/UUID
    - https://eats.uber.com/group-orders/UUID
    - https://eats.uber.com/group-orders/UUID/join

    Args:
        group_link: Full Uber Eats group order URL

    Returns:
        Group order UUID string

    Raises:
        ValueError: If UUID cannot be extracted from link
    """
    # Remove /join suffix if present
    if group_link.endswith('/join'):
        group_link = group_link[:-5]

    # Pattern to match UUID in group-orders URLs
    pattern = r'group-orders/([a-zA-Z0-9-]+)'
    match = re.search(pattern, group_link)

    if not match:
        raise ValueError(f"Invalid Uber Eats group order link format: {group_link}")

    uuid = match.group(1)
    logger.info(f"Extracted group UUID: {uuid} from link: {group_link}")
    return uuid


async def extract_complete_cart_info(group_order_uuid: str, session: Optional[aiohttp.ClientSession] = None) -> Dict[str, Any]:
    """
    Extract complete cart information with full item structures from group order.

    This uses getDraftOrderByUuidV2 to get items with complete customizations,
    subsection UUIDs, and all required fields for createDraftOrderV2.

    Args:
        group_order_uuid: UUID of the group order to extract from
        session: Optional existing aiohttp session

    Returns:
        Dictionary containing complete cart information with ready-to-use item structures
    """
    session_created = False

    try:
        # Create session if not provided
        if session is None:
            cookies = parse_cookies(PERSONAL_ACCOUNT_COOKIES)
            session = aiohttp.ClientSession(cookies=cookies)
            session_created = True

        logger.info(f"Extracting complete cart info for group order: {group_order_uuid}")

        # CRITICAL: Join the group order first to get access to complete data
        join_payload = {
            "draftOrderUuid": group_order_uuid
        }

        logger.info("Joining group order as member to access complete item structures")
        join_result = await make_api_request(session, "addMemberToDraftOrderV1", join_payload, use_personal_headers=True)

        if not join_result.get('success'):
            logger.warning(f"Failed to join group order: {join_result.get('error', 'Unknown error')}")
            # Continue anyway - might still work if already a member
        else:
            logger.info("Successfully joined group order as member")

        # Use getDraftOrderByUuidV2 for complete structure (now as a member)
        payload = {
            "draftOrderUUID": group_order_uuid
        }

        result = await make_api_request(session, "getDraftOrderByUuidV2", payload, use_personal_headers=True)

        if not result.get('success'):
            logger.error("Failed to get draft order V2")
            return {'success': False, 'error': 'getDraftOrderByUuidV2 request failed'}

        # Parse the response
        response_data = result.get('data', {})
        data_section = response_data.get('data', {})

        # Extract draft order
        draft_order = data_section.get('draftOrder', {})
        if not draft_order:
            logger.error("No draft order in getDraftOrderByUuidV2 response")
            return {'success': False, 'error': 'No draft order found'}

        # Extract shopping cart with complete items
        shopping_cart = draft_order.get('shoppingCart', {})
        if not shopping_cart:
            logger.error("No shopping cart in draft order")
            return {'success': False, 'error': 'No shopping cart found'}

        # Get complete items (these have all customizations and UUIDs)
        complete_items = shopping_cart.get('items', [])

        # Extract store information
        store_uuid = draft_order.get('storeUuid', '')

        # Try to get store name from business details or other locations
        store_name = draft_order.get('storeName', '')
        if not store_name:
            business_details = draft_order.get('businessDetails', {})
            store_name = business_details.get('name', '')

        store_info = {
            'uuid': store_uuid,
            'name': store_name,
            'address': draft_order.get('storeAddress', '')
        }

        # Extract delivery address from the correct structure
        delivery_address_raw = draft_order.get('deliveryAddress', {})

        # Parse the address structure correctly
        delivery_address = {}
        if delivery_address_raw:
            address_info = delivery_address_raw.get('address', {})
            address_components = delivery_address_raw.get('addressComponents', {})

            # Extract address fields from the correct locations
            delivery_address = {
                'address1': address_info.get('address1', ''),
                'address2': address_info.get('address2', ''),
                'city': address_components.get('city', ''),
                'state': address_components.get('firstLevelSubdivisionCode', ''),
                'zipcode': address_components.get('postalCode', ''),
                'country': address_components.get('countryCode', ''),
                'latitude': delivery_address_raw.get('latitude', 0.0),
                'longitude': delivery_address_raw.get('longitude', 0.0),
                'formatted_address': address_info.get('title', ''),
                'subtitle': address_info.get('subtitle', ''),  # Often contains "City, State"
                'reference': delivery_address_raw.get('reference', ''),
                'reference_type': delivery_address_raw.get('referenceType', ''),
                # CRITICAL: Include the raw structure for exact replication
                'raw_delivery_address': delivery_address_raw
            }

            # If city/state are empty in addressComponents, try to parse from subtitle
            if not delivery_address['city'] and delivery_address['subtitle']:
                # subtitle is often "Philadelphia, PA" format
                subtitle_parts = delivery_address['subtitle'].split(', ')
                if len(subtitle_parts) >= 2:
                    delivery_address['city'] = subtitle_parts[0]
                    delivery_address['state'] = subtitle_parts[1]

            logger.info(f"Extracted delivery address: {delivery_address['address1']}, {delivery_address['city']}, {delivery_address['state']}")
        else:
            logger.warning("No delivery address found in group order")

        # Calculate pricing from items (convert from cents to dollars)
        total_price_cents = sum(item.get('price', 0) * item.get('quantity', 1) for item in complete_items)
        total_price_dollars = total_price_cents / 100.0  # Convert cents to dollars

        logger.info(f"Pricing calculation: {total_price_cents} cents = ${total_price_dollars:.2f}")

        pricing_breakdown = {
            'subtotal': total_price_dollars,
            'total': total_price_dollars
        }

        # Detect currency
        currency_code = shopping_cart.get('currencyCode', 'USD')
        is_cad = currency_code == 'CAD'
        currency_symbol = 'C$' if is_cad else '$'

        logger.info(f"Extracted {len(complete_items)} complete items with full structure")

        return {
            'success': True,
            'extraction_method': 'getDraftOrderByUuidV2_complete',
            'cart_items': complete_items,  # These are complete and ready to use!
            'store_info': store_info,
            'delivery_address': delivery_address,
            'pricing_breakdown': pricing_breakdown,
            'currency_info': {
                'is_cad': is_cad,
                'symbol': currency_symbol,
                'code': currency_code
            },
            'raw_data': draft_order
        }

    except Exception as e:
        logger.error(f"Error extracting complete cart info: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'cart_items': [],
            'store_info': {},
            'delivery_address': {},
            'pricing_breakdown': {},
            'currency_info': {'is_cad': False, 'symbol': '$', 'code': 'USD'}
        }

    finally:
        # Clean up session if we created it
        if session_created and session:
            await session.close()


async def extract_cart_info(group_order_uuid: str, session: Optional[aiohttp.ClientSession] = None) -> Dict[str, Any]:
    """
    Extract comprehensive cart information from an existing group order.
    
    This function uses the getCheckoutPresentationV1 endpoint as the primary method
    to retrieve cart details, with getDraftOrderByUuidV1 as a fallback.
    
    Args:
        group_order_uuid: UUID of the group order to extract from
        session: Optional existing aiohttp session
        
    Returns:
        Dictionary containing extracted cart information including:
        - cart_items: List of items with quantities and customizations
        - store_info: Store UUID and location details
        - delivery_address: Delivery location information
        - pricing_breakdown: Current pricing data
        - currency_info: Currency type and formatting
    """
    session_created = False
    
    try:
        # Create session if not provided
        if session is None:
            cookies = parse_cookies(DEFAULT_COOKIE_STRING)
            session = aiohttp.ClientSession(cookies=cookies)
            session_created = True
        
        logger.info(f"Extracting cart info for group order: {group_order_uuid}")
        
        # Primary method: Use getCheckoutPresentationV1
        cart_info = await _extract_via_checkout_presentation(session, group_order_uuid)
        
        if cart_info and cart_info.get('success'):
            logger.info("Successfully extracted cart info via checkout presentation")
            return cart_info
        
        # Fallback method: Use getDraftOrderByUuidV1
        logger.warning("Primary extraction failed, trying fallback method")
        cart_info = await _extract_via_draft_order(session, group_order_uuid)
        
        if cart_info and cart_info.get('success'):
            logger.info("Successfully extracted cart info via draft order fallback")
            return cart_info
        
        # Both methods failed
        logger.error("All extraction methods failed")
        return {
            'success': False,
            'error': 'Failed to extract cart information using all available methods',
            'cart_items': [],
            'store_info': {},
            'delivery_address': {},
            'pricing_breakdown': {},
            'currency_info': {'is_cad': False, 'symbol': '$'}
        }
        
    except Exception as e:
        logger.error(f"Error extracting cart info: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'cart_items': [],
            'store_info': {},
            'delivery_address': {},
            'pricing_breakdown': {},
            'currency_info': {'is_cad': False, 'symbol': '$'}
        }
        
    finally:
        # Clean up session if we created it
        if session_created and session:
            await session.close()


async def _extract_via_checkout_presentation(session: aiohttp.ClientSession, group_uuid: str) -> Dict[str, Any]:
    """
    Extract cart information using the getCheckoutPresentationV1 endpoint.
    
    Args:
        session: Authenticated aiohttp session
        group_uuid: Group order UUID
        
    Returns:
        Dictionary containing extracted cart information
    """
    try:
        # First, we need to get basic order info to extract store and cart UUIDs
        draft_payload = {
            "draftOrderUuid": group_uuid
        }
        
        draft_result = await make_api_request(session, "getDraftOrderByUuidV1", draft_payload)
        
        if not draft_result.get('success'):
            logger.error("Failed to get draft order for checkout presentation")
            return {'success': False, 'error': 'Failed to get draft order info'}
        
        # Extract necessary UUIDs from draft order response
        draft_data = draft_result.get('data', {}).get('data', {})
        store_uuid = draft_data.get('storeUuid') or draft_data.get('storeId')
        cart_uuid = draft_data.get('cartUuid') or draft_data.get('cartId')

        # Try alternative extraction paths
        if not store_uuid:
            store_uuid = draft_data.get('store', {}).get('uuid') or draft_data.get('store', {}).get('id')
        if not cart_uuid:
            cart_uuid = draft_data.get('cart', {}).get('uuid') or draft_data.get('cart', {}).get('id')

        if not store_uuid or not cart_uuid:
            logger.error(f"Missing store or cart UUID from draft order. Available keys: {list(draft_data.keys())}")
            logger.debug(f"Draft data: {json.dumps(draft_data, indent=2)}")
            return {'success': False, 'error': 'Missing required UUIDs'}
        
        # Now get checkout presentation with comprehensive payload types
        checkout_payload = {
            "payloadTypes": [
                "canonicalProductStorePickerPayload",
                "cartItems",
                "subtotal",
                "basketSize",
                "promotion",
                "restrictedItems",
                "venueSectionPicker",
                "locationInfo",
                "upsellCatalogSections",
                "subTotalFareBreakdown",
                "storeSwitcherActionableBannerPayload",
                "fareBreakdown",
                "promoAndMembershipSavingBannerPayload",
                "passBanner",
                "passBannerOnCartPayload",
                "cartItemsV2",
                "storeInfo"
            ],
            "draftOrderUUID": group_uuid,
            "isGroupOrder": True,
            "cartUUID": cart_uuid,
            "storeUUID": store_uuid
        }
        
        result = await make_api_request(session, "getCheckoutPresentationV1", checkout_payload)
        
        if not result.get('success'):
            logger.error("Failed to get checkout presentation")
            return {'success': False, 'error': 'Checkout presentation request failed'}
        
        # Parse the comprehensive response
        response_data = result.get('data', {})
        checkout_data = response_data.get('data', {}).get('checkoutPayloads', {})
        
        # Extract cart items
        cart_items = _parse_cart_items(checkout_data)
        
        # Extract store information
        store_info = _parse_store_info(checkout_data, store_uuid)
        
        # Extract delivery address
        delivery_address = _parse_delivery_address(checkout_data)
        
        # Extract pricing breakdown
        pricing_breakdown = _parse_pricing_breakdown(checkout_data)
        
        # Detect currency
        is_cad, currency_symbol = detect_currency(pricing_breakdown)
        
        return {
            'success': True,
            'extraction_method': 'checkout_presentation',
            'cart_items': cart_items,
            'store_info': store_info,
            'delivery_address': delivery_address,
            'pricing_breakdown': pricing_breakdown,
            'currency_info': {
                'is_cad': is_cad,
                'symbol': currency_symbol
            },
            'raw_data': checkout_data  # Include raw data for debugging
        }
        
    except Exception as e:
        logger.error(f"Error in checkout presentation extraction: {str(e)}")
        return {'success': False, 'error': str(e)}


async def _extract_via_draft_order(session: aiohttp.ClientSession, group_uuid: str) -> Dict[str, Any]:
    """
    Fallback method to extract cart information using addMemberToDraftOrderV1 endpoint.

    Args:
        session: Authenticated aiohttp session
        group_uuid: Group order UUID

    Returns:
        Dictionary containing extracted cart information
    """
    try:
        # Use join method as fallback since getDraftOrderByUuidV1 often returns UNAUTHORIZED_ACCESS
        payload = {
            "draftOrderUuid": group_uuid,
            "nickname": "PriceChecker"
        }

        result = await make_api_request(session, "addMemberToDraftOrderV1", payload)

        if not result.get('success'):
            logger.error("Failed to join group order")
            return {'success': False, 'error': 'Join group order request failed'}

        # Parse the response
        response_data = result.get('data', {})
        draft_data = response_data.get('data', {})

        # Check for error messages
        if 'message' in draft_data and 'code' in draft_data:
            error_msg = draft_data.get('message', '')
            if 'UNAUTHORIZED' in error_msg or 'locked' in error_msg.lower():
                logger.error(f"Group order access denied: {error_msg}")
                return {'success': False, 'error': f'Access denied: {error_msg}'}

        # Extract basic information
        cart_items = _parse_draft_cart_items(draft_data)
        store_info = _parse_draft_store_info(draft_data)
        delivery_address = _parse_draft_delivery_address(draft_data)
        pricing_breakdown = _parse_draft_pricing(draft_data)

        logger.info(f"Extract complete cart info - Found {len(cart_items)} items")

        # Calculate basic pricing from cart items if not available or incorrect
        if cart_items:
            # Always calculate from cart items to ensure accuracy (prices are stored in cents)
            calculated_subtotal = sum((item.get('price', 0) / 100.0) * item.get('quantity', 1) for item in cart_items)

            # Debug logging
            logger.info(f"Cart items pricing debug:")
            for item in cart_items:
                price_cents = item.get('price', 0)
                quantity = item.get('quantity', 1)
                price_dollars = price_cents / 100.0
                logger.info(f"  {item.get('title', 'Unknown')}: {price_cents} cents = ${price_dollars:.2f} x {quantity}")
            logger.info(f"Calculated subtotal: ${calculated_subtotal:.2f}")

            # Use calculated subtotal if draft pricing is missing or seems wrong (too high)
            draft_subtotal = pricing_breakdown.get('subtotal', 0)
            draft_total = pricing_breakdown.get('total', 0)
            logger.info(f"Draft pricing - Subtotal: ${draft_subtotal:.2f}, Total: ${draft_total:.2f}")

            if not draft_subtotal or draft_subtotal > calculated_subtotal * 10:  # Draft price is 10x higher, likely in cents
                logger.info(f"Using calculated subtotal ${calculated_subtotal:.2f} instead of draft subtotal ${draft_subtotal:.2f}")
                pricing_breakdown['subtotal'] = calculated_subtotal
                pricing_breakdown['total'] = calculated_subtotal  # Basic total without fees
            else:
                logger.info(f"Using draft pricing - Subtotal: ${draft_subtotal:.2f}, Total: ${draft_total:.2f}")

        # Detect currency
        is_cad, currency_symbol = detect_currency(pricing_breakdown)

        return {
            'success': True,
            'extraction_method': 'join_group_order',
            'cart_items': cart_items,
            'store_info': store_info,
            'delivery_address': delivery_address,
            'pricing_breakdown': pricing_breakdown,
            'currency_info': {
                'is_cad': is_cad,
                'symbol': currency_symbol
            },
            'raw_data': draft_data
        }

    except Exception as e:
        logger.error(f"Error in draft order extraction: {str(e)}")
        return {'success': False, 'error': str(e)}


def _parse_cart_items(checkout_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Parse cart items from checkout presentation data.

    Args:
        checkout_data: Checkout presentation payload data

    Returns:
        List of cart items with quantities, customizations, and IDs
    """
    cart_items = []

    try:
        # Try cartItemsV2 first (more detailed)
        items_data = checkout_data.get('cartItemsV2', {}).get('cartItems', [])

        if not items_data:
            # Fallback to regular cartItems
            items_data = checkout_data.get('cartItems', {}).get('cartItems', [])

        for item in items_data:
            item_info = {
                'item_id': item.get('uuid', ''),
                'product_id': item.get('productId', ''),
                'name': item.get('title', ''),
                'quantity': item.get('quantity', 1),
                'price': item.get('price', 0),
                'customizations': [],
                'special_instructions': item.get('specialInstructions', ''),
                'raw_item_data': item  # Keep raw data for reconstruction
            }

            # Parse customizations
            customizations = item.get('customizations', [])
            for customization in customizations:
                custom_info = {
                    'customization_id': customization.get('uuid', ''),
                    'name': customization.get('title', ''),
                    'options': []
                }

                # Parse customization options
                options = customization.get('options', [])
                for option in options:
                    option_info = {
                        'option_id': option.get('uuid', ''),
                        'name': option.get('title', ''),
                        'price': option.get('price', 0),
                        'selected': option.get('selected', False)
                    }
                    custom_info['options'].append(option_info)

                item_info['customizations'].append(custom_info)

            cart_items.append(item_info)

        logger.info(f"Parsed {len(cart_items)} cart items")
        return cart_items

    except Exception as e:
        logger.error(f"Error parsing cart items: {str(e)}")
        return []


def _parse_store_info(checkout_data: Dict[str, Any], store_uuid: str) -> Dict[str, Any]:
    """
    Parse store information from checkout presentation data.

    Args:
        checkout_data: Checkout presentation payload data
        store_uuid: Store UUID

    Returns:
        Dictionary containing store information
    """
    try:
        store_info = {
            'store_uuid': store_uuid,
            'name': '',
            'address': '',
            'phone': '',
            'cuisine_type': '',
            'rating': 0.0,
            'delivery_fee': 0.0,
            'minimum_order': 0.0
        }

        # Try to get store info from various payload sections
        canonical_store = checkout_data.get('canonicalProductStorePickerPayload', {})
        if canonical_store:
            store_data = canonical_store.get('store', {})
            store_info.update({
                'name': store_data.get('title', ''),
                'address': store_data.get('location', {}).get('address', ''),
                'rating': store_data.get('rating', {}).get('ratingValue', 0.0),
                'cuisine_type': store_data.get('cuisineType', '')
            })

        # Get additional store info if available
        store_payload = checkout_data.get('storeInfo', {})
        if store_payload:
            store_info.update({
                'phone': store_payload.get('phone', ''),
                'minimum_order': store_payload.get('minimumOrderValue', 0.0)
            })

        logger.info(f"Parsed store info for: {store_info.get('name', 'Unknown Store')}")
        return store_info

    except Exception as e:
        logger.error(f"Error parsing store info: {str(e)}")
        return {'store_uuid': store_uuid}


def _parse_delivery_address(checkout_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse delivery address from checkout presentation data.

    Args:
        checkout_data: Checkout presentation payload data

    Returns:
        Dictionary containing delivery address information
    """
    try:
        delivery_address = {
            'address1': '',
            'address2': '',
            'city': '',
            'state': '',
            'zipcode': '',
            'country': '',
            'latitude': 0.0,
            'longitude': 0.0,
            'formatted_address': ''
        }

        # Try to get location info from locationInfo payload
        location_info = checkout_data.get('locationInfo', {})
        if location_info:
            location_data = location_info.get('location', {})
            address_data = location_data.get('address', {})

            delivery_address.update({
                'address1': address_data.get('address1', ''),
                'address2': address_data.get('address2', ''),
                'city': address_data.get('city', ''),
                'state': address_data.get('state', ''),
                'zipcode': address_data.get('zipcode', ''),
                'country': address_data.get('country', ''),
                'latitude': location_data.get('latitude', 0.0),
                'longitude': location_data.get('longitude', 0.0),
                'formatted_address': address_data.get('formattedAddress', '')
            })

        logger.info(f"Parsed delivery address: {delivery_address.get('formatted_address', 'Unknown Address')}")
        return delivery_address

    except Exception as e:
        logger.error(f"Error parsing delivery address: {str(e)}")
        return delivery_address


def _parse_pricing_breakdown(checkout_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse pricing breakdown from checkout presentation data.

    Args:
        checkout_data: Checkout presentation payload data

    Returns:
        Dictionary containing detailed pricing breakdown
    """
    try:
        pricing = {
            'subtotal': 0.0,
            'delivery_fee': 0.0,
            'service_fee': 0.0,
            'ca_driver_benefit': 0.0,
            'taxes': 0.0,
            'total_fees': 0.0,
            'uber_one_discount': 0.0,
            'promotions': [],
            'total': 0.0
        }

        # Parse fare breakdown
        fare_breakdown = checkout_data.get('fareBreakdown', {})
        if fare_breakdown:
            charges = fare_breakdown.get('charges', [])

            for charge in charges:
                title = charge.get('title', {}).get('text', '').lower()
                value_text = charge.get('value', {}).get('text', '0.00')

                # Handle non-breaking space in value text
                if '\u00a0' in value_text:
                    value_text = value_text.split('\u00a0')[-1]

                # Clean and convert value
                value = float(re.sub(r'[^\d.]', '', value_text.replace('CA', '')))

                if 'subtotal' in title:
                    pricing['subtotal'] = value
                elif 'delivery fee' in title:
                    pricing['delivery_fee'] = value
                elif 'taxes & other fees' in title:
                    # Parse detailed fees from info bottom sheet
                    paragraphs = charge.get('action', {}).get('infoBottomSheet', {}).get('paragraphs', [])
                    for paragraph in paragraphs:
                        p_title = paragraph.get('title', '').lower()
                        end_title = paragraph.get('endTitle', '$0.00')
                        p_value = float(re.sub(r'[^\d.]', '', end_title.replace('CA', '')))

                        if 'service fee' in p_title:
                            pricing['service_fee'] = p_value
                        elif 'ca driver benefit' in p_title:
                            pricing['ca_driver_benefit'] = p_value
                        elif 'taxes' == p_title:
                            pricing['taxes'] = p_value

        # Parse Uber One discount from pass banner
        pass_banner = checkout_data.get('passBanner', {})
        if pass_banner and pass_banner.get('banners'):
            savings = pass_banner['banners'][0].get('savingsAmountWithCurrencySymbol', '$0.00')
            pricing['uber_one_discount'] = float(re.sub(r'[^\d.]', '', savings))

        # Calculate totals
        pricing['total_fees'] = (pricing['service_fee'] + pricing['delivery_fee'] +
                               pricing['ca_driver_benefit'] + pricing['taxes'])
        pricing['total'] = pricing['subtotal'] + pricing['total_fees'] - pricing['uber_one_discount']

        logger.info(f"Parsed pricing - Subtotal: ${pricing['subtotal']:.2f}, Total: ${pricing['total']:.2f}")
        return pricing

    except Exception as e:
        logger.error(f"Error parsing pricing breakdown: {str(e)}")
        return pricing


def _parse_draft_cart_items(draft_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Parse cart items from draft order data (fallback method).

    Args:
        draft_data: Draft order response data

    Returns:
        List of cart items
    """
    try:
        cart_items = []

        # Try multiple paths to find cart items
        items_data = []

        # Path 1: Direct cartItems
        if 'cartItems' in draft_data:
            items_data = draft_data['cartItems']

        # Path 2: shoppingCart.items (from join response)
        elif 'shoppingCart' in draft_data and 'items' in draft_data['shoppingCart']:
            items_data = draft_data['shoppingCart']['items']

        # Path 3: cart.items
        elif 'cart' in draft_data and 'items' in draft_data['cart']:
            items_data = draft_data['cart']['items']

        for item in items_data:
            # Keep price in cents (Uber Eats uses cents internally)
            price = item.get('price', 0)
            # Don't convert to dollars - keep in cents for consistency

            item_info = {
                'item_id': item.get('uuid', '') or item.get('shoppingCartItemUuid', ''),
                'product_id': item.get('productId', '') or item.get('uuid', ''),
                'name': item.get('title', ''),
                'quantity': item.get('quantity', 1),
                'price': price,
                'customizations': _parse_item_customizations(item),
                'special_instructions': item.get('specialInstructions', ''),
                # Extract additional metadata needed for proper cart creation
                'section_uuid': item.get('sectionUuid', ''),
                'subsection_uuid': item.get('subsectionUuid', ''),
                'image_url': item.get('imageURL', ''),
                'store_uuid': item.get('storeUuid', ''),
                'availability_status': item.get('availabilityStatus', 'AVAILABLE'),
                'is_available': item.get('isAvailable', True),
                'raw_item_data': item
            }
            cart_items.append(item_info)

        logger.info(f"Parsed {len(cart_items)} cart items from draft order")
        return cart_items

    except Exception as e:
        logger.error(f"Error parsing draft cart items: {str(e)}")
        return []


async def _fetch_fresh_item_data_basic(session: aiohttp.ClientSession, store_uuid: str, item_name: str) -> Dict[str, Any]:
    """
    FIXED method for fetching fresh item data using the correct catalogSectionsMap structure.

    Items are located in catalogSectionsMap -> list -> payload -> standardItemsPayload -> catalogItems
    """
    try:
        logger.info(f"Using FIXED method to fetch item data for '{item_name}' from store {store_uuid}")

        payload = {
            "storeUuid": store_uuid,
            "diningMode": "DELIVERY",
            "time": {"asap": True},
            "cbType": "EATER_ENDORSED"
        }

        result = await make_api_request(session, "getStoreV1", payload, use_personal_headers=True)

        if not result.get('success'):
            logger.error(f"Failed to fetch store data: {result.get('error')}")
            return {}

        response_data = result.get('data', {})
        store_data = response_data.get('data', {})

        # FIXED: Extract items from catalogSectionsMap with COMPLETE structure including subsectionUuid
        all_items = []

        # Method 1: catalogSectionsMap with correct structure
        catalog_sections_map = store_data.get('catalogSectionsMap', {})
        subsections_map = store_data.get('subsectionsMap', {})

        for section_id, section_data in catalog_sections_map.items():
            if isinstance(section_data, list):
                # section_data is a list of items with payload structure
                for wrapper_item in section_data:
                    if isinstance(wrapper_item, dict) and 'payload' in wrapper_item:
                        payload_data = wrapper_item['payload']

                        if isinstance(payload_data, dict) and 'standardItemsPayload' in payload_data:
                            standard_items = payload_data['standardItemsPayload']

                            if isinstance(standard_items, dict):
                                catalog_items = standard_items.get('catalogItems', [])

                                for item in catalog_items:
                                    if isinstance(item, dict):
                                        # Find the correct subsection UUID for this item
                                        subsection_uuid = ''

                                        # Look through subsectionsMap to find which subsection contains this item
                                        for sub_id, sub_data in subsections_map.items():
                                            if isinstance(sub_data, dict):
                                                sub_items = sub_data.get('catalogItems', [])
                                                for sub_item in sub_items:
                                                    if isinstance(sub_item, dict) and sub_item.get('uuid') == item.get('uuid'):
                                                        subsection_uuid = sub_id
                                                        break
                                                if subsection_uuid:
                                                    break

                                        all_items.append({
                                            'uuid': item.get('uuid', ''),
                                            'title': item.get('title', item.get('name', '')),
                                            'price': item.get('price', 0),
                                            'section_uuid': section_id,
                                            'subsection_uuid': subsection_uuid,  # Now properly populated!
                                            'image_url': item.get('imageURL', ''),
                                            'customizations': item.get('customizations', {}),
                                            'availability_status': item.get('availabilityStatus', 'AVAILABLE'),
                                            'is_available': item.get('isAvailable', True)
                                        })

        logger.info(f"Extracted {len(all_items)} items using FIXED method")

        # Find matching item
        for item in all_items:
            item_title = item.get('title', '')
            if item_name.lower() in item_title.lower() or item_title.lower() in item_name.lower():
                logger.info(f"Found matching item: '{item_title}' with UUID: {item['uuid']}")
                return item

        logger.warning(f"Item '{item_name}' not found in {len(all_items)} extracted items")

        # Log available items for debugging
        if all_items:
            logger.info("Available items:")
            for i, item in enumerate(all_items[:5], 1):
                logger.info(f"  {i}. {item['title']} (${item['price']/100:.2f})")
            if len(all_items) > 5:
                logger.info(f"  ... and {len(all_items) - 5} more items")

        return {}

    except Exception as e:
        logger.error(f"Error in FIXED item data fetch: {str(e)}")
        return {}


def _get_store_slug_from_uuid(store_uuid: str) -> Optional[str]:
    """
    Attempt to get store slug from UUID using known mappings or patterns.

    This is a best-effort function that may not work for all stores.
    """
    # Known mappings for testing
    known_mappings = {
        "729815d7-d8b1-5eff-acfa-98efca5f4f5c": "shake-shack-stonestown-galleria/cpgV19ixXv-s-pjvyl9PXA",
        "a4d9e2ad-ee4a-4aed-880e-2f7a62862cd2": None  # Unknown slug for this store
    }

    return known_mappings.get(store_uuid)


async def _fetch_fresh_item_data(session: aiohttp.ClientSession, store_uuid: str, item_name: str, store_slug: str = None) -> Dict[str, Any]:
    """
    Fetch fresh item data from the store menu using comprehensive retrieval methods.

    Group order item UUIDs are not valid for personal carts, so we need to fetch
    the same item from the store's current menu using multiple approaches.

    Args:
        session: Authenticated aiohttp session
        store_uuid: Store UUID to fetch menu from
        item_name: Name of the item to find
        store_slug: Optional store slug for browser scraping fallback

    Returns:
        Dictionary containing fresh item data or empty dict if not found
    """
    try:
        logger.info(f"Fetching fresh item data for '{item_name}' from store {store_uuid}")

        # Import the comprehensive menu retriever
        try:
            from comprehensive_menu_solution import MenuDataRetriever, find_matching_item
        except ImportError:
            logger.error("comprehensive_menu_solution module not found - falling back to basic method")
            return await _fetch_fresh_item_data_basic(session, store_uuid, item_name)

        # Use the comprehensive menu data retrieval system
        async with MenuDataRetriever() as retriever:
            menu_result = await retriever.get_menu_data(store_uuid, store_slug)

            if not menu_result.get('items'):
                logger.warning(f"No menu items found for store {store_uuid}")
                return {}

            menu_items = menu_result['items']
            logger.info(f"Retrieved {len(menu_items)} menu items using method: {menu_result.get('method', 'unknown')}")

            # Find matching item
            matching_item = await find_matching_item(menu_items, item_name)

            if matching_item:
                logger.info(f"Found matching item: '{matching_item['title']}' (UUID: {matching_item['uuid']})")
                return matching_item
            else:
                logger.warning(f"Item '{item_name}' not found in {len(menu_items)} menu items")

                # Log available items for debugging
                logger.info("Available items:")
                for item in menu_items[:5]:  # Show first 5 items
                    logger.info(f"  - {item['title']} (${item['price']/100:.2f})")
                if len(menu_items) > 5:
                    logger.info(f"  ... and {len(menu_items) - 5} more items")

                return {}

    except Exception as e:
        logger.error(f"Error fetching fresh item data: {str(e)}")
        return {}


def _parse_item_customizations(item: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Parse customizations from item data.

    Args:
        item: Item data containing customizations

    Returns:
        List of customizations with options
    """
    try:
        customizations = []

        # Check for customizations in the item data
        item_customizations = item.get('customizations', {})

        if isinstance(item_customizations, dict):
            for custom_key, custom_options in item_customizations.items():
                # Parse customization key (format: "uuid+index")
                customization_id = custom_key.split('+')[0] if '+' in custom_key else custom_key

                customization = {
                    'customization_id': customization_id,
                    'name': f'Customization {customization_id}',  # Default name
                    'options': []
                }

                if isinstance(custom_options, list):
                    for option in custom_options:
                        if isinstance(option, dict):
                            option_info = {
                                'option_id': option.get('uuid', ''),
                                'name': option.get('title', ''),
                                'price': option.get('price', 0) / 100.0 if option.get('price', 0) > 100 else option.get('price', 0),
                                'selected': True,  # If it's in the cart, it's selected
                                'quantity': option.get('quantity', 1)
                            }
                            customization['options'].append(option_info)

                if customization['options']:
                    customizations.append(customization)

        return customizations

    except Exception as e:
        logger.error(f"Error parsing item customizations: {str(e)}")
        return []


def _parse_draft_store_info(draft_data: Dict[str, Any]) -> Dict[str, Any]:
    """Parse store info from draft order data."""
    try:
        # Extract store UUID - this is the key field we need
        store_uuid = draft_data.get('storeUuid', '')

        # Try to get store UUID from shopping cart if not found directly
        if not store_uuid and 'shoppingCart' in draft_data:
            store_uuid = draft_data['shoppingCart'].get('storeUuid', '')

        store_info = {
            'uuid': store_uuid,  # Fixed: comprehensive test expects 'uuid', not 'store_uuid'
            'name': draft_data.get('storeName', ''),
            'address': draft_data.get('storeAddress', ''),
            'phone': '',
            'cuisine_type': '',
            'rating': 0.0,
            'delivery_fee': 0.0,
            'minimum_order': 0.0
        }

        return store_info
    except Exception as e:
        logger.error(f"Error parsing draft store info: {str(e)}")
        return {}


def _parse_draft_delivery_address(draft_data: Dict[str, Any]) -> Dict[str, Any]:
    """Parse delivery address from draft order data."""
    try:
        # Try multiple paths for delivery address
        delivery_info = draft_data.get('deliveryInfo', {})

        # Also try deliveryAddress from join response
        if not delivery_info and 'deliveryAddress' in draft_data:
            delivery_info = draft_data['deliveryAddress']

        return {
            'address1': delivery_info.get('address1', '') or delivery_info.get('streetAddress', ''),
            'address2': delivery_info.get('address2', '') or delivery_info.get('aptOrSuite', ''),
            'city': delivery_info.get('city', ''),
            'state': delivery_info.get('state', ''),
            'zipcode': delivery_info.get('zipcode', '') or delivery_info.get('postalCode', ''),
            'country': delivery_info.get('country', ''),
            'latitude': delivery_info.get('latitude', 0.0),
            'longitude': delivery_info.get('longitude', 0.0),
            'formatted_address': delivery_info.get('formattedAddress', '') or delivery_info.get('title', '')
        }
    except Exception as e:
        logger.error(f"Error parsing draft delivery address: {str(e)}")
        return {}


def _parse_draft_pricing(draft_data: Dict[str, Any]) -> Dict[str, Any]:
    """Parse pricing from draft order data."""
    try:
        # Convert from cents to dollars for all pricing fields
        def convert_cents_to_dollars(value):
            """Convert cents to dollars if value appears to be in cents."""
            if isinstance(value, (int, float)) and value > 100:
                return value / 100.0
            return float(value) if value else 0.0

        return {
            'subtotal': convert_cents_to_dollars(draft_data.get('subtotal', 0.0)),
            'delivery_fee': convert_cents_to_dollars(draft_data.get('deliveryFee', 0.0)),
            'service_fee': convert_cents_to_dollars(draft_data.get('serviceFee', 0.0)),
            'ca_driver_benefit': 0.0,
            'taxes': convert_cents_to_dollars(draft_data.get('taxes', 0.0)),
            'total_fees': convert_cents_to_dollars(draft_data.get('totalFees', 0.0)),
            'uber_one_discount': 0.0,
            'promotions': [],
            'total': convert_cents_to_dollars(draft_data.get('total', 0.0))
        }
    except Exception as e:
        logger.error(f"Error parsing draft pricing: {str(e)}")
        return {}


def extract_user_uuid_from_cookies(cookie_string: str) -> str:
    """
    Extract user UUID from personal account cookies.

    Based on captured request, the user UUID is in _userUuid=4867ed9b-090d-4068-b725-85e3d2d0ffa3

    Args:
        cookie_string: Personal account cookie string

    Returns:
        User UUID for API calls
    """
    try:
        cookies = parse_cookies(cookie_string)

        # Primary: _userUuid (from captured request)
        user_uuid = cookies.get('_userUuid')

        if user_uuid and user_uuid != 'undefined':
            logger.info(f"Extracted user UUID from _userUuid: {user_uuid}")
            return user_uuid

        # Fallback options
        fallback_uuid = (cookies.get('uev2.id.xp') or
                        cookies.get('dId'))

        if fallback_uuid and fallback_uuid != 'undefined':
            logger.info(f"Extracted user UUID from fallback: {fallback_uuid}")
            return fallback_uuid

        logger.error("No valid user UUID found in cookies")
        logger.debug(f"Available cookie keys: {list(cookies.keys())}")
        return ""

    except Exception as e:
        logger.error(f"Error extracting user UUID: {str(e)}")
        return ""


async def add_items_to_personal_cart(cart_data: Dict[str, Any], personal_cookies: str = None, include_checkout_simulation: bool = True) -> Dict[str, Any]:
    """
    Add extracted cart items to your personal Uber Eats cart using the correct two-step process.

    Step 1: Create draft order with first item using createDraftOrderV2
    Step 2: Add remaining items using addItemsToDraftOrderV2

    Args:
        cart_data: Cart information extracted from extract_cart_info()
        personal_cookies: Your personal account cookie string

    Returns:
        Dictionary containing result of adding items to personal cart
    """
    try:
        if not personal_cookies or personal_cookies.strip() == "REPLACE_WITH_YOUR_PERSONAL_ACCOUNT_COOKIES":
            return {
                'success': False,
                'error': 'Personal account cookies not configured. Please set your authentication data.',
                'instructions': 'Use set_personal_authentication() to configure your credentials.'
            }

        if not cart_data.get('success'):
            logger.error("Cannot add items from failed cart extraction")
            return {'success': False, 'error': 'Invalid cart data provided'}

        logger.info("Starting to add items to personal cart using correct two-step process")

        # Parse personal account cookies
        cookies = parse_cookies(personal_cookies)
        user_uuid = extract_user_uuid_from_cookies(personal_cookies)

        if not user_uuid:
            return {
                'success': False,
                'error': 'Could not extract user UUID from personal cookies'
            }

        # Create authenticated session with personal account
        session = aiohttp.ClientSession(cookies=cookies)

        try:
            # Get store and cart information
            store_info = cart_data.get('store_info', {})
            # Handle both 'uuid' and 'store_uuid' for compatibility
            store_uuid = store_info.get('uuid') or store_info.get('store_uuid')
            cart_items = cart_data.get('cart_items', [])

            if not store_uuid:
                return {
                    'success': False,
                    'error': 'No store UUID found in cart data'
                }

            if not cart_items:
                return {
                    'success': False,
                    'error': 'No items found in cart data'
                }

            logger.info(f"Adding {len(cart_items)} items to personal cart for store {store_uuid}")

            # Step 1: Filter out unavailable items
            available_items = []
            unavailable_items = []

            for item in cart_items:
                is_available = item.get('is_available', True)
                availability_status = item.get('availability_status', 'AVAILABLE')

                if is_available and availability_status.upper() == 'AVAILABLE':
                    available_items.append(item)
                else:
                    unavailable_items.append({
                        'name': item.get('name', 'Unknown Item'),
                        'error': f'Item unavailable (status: {availability_status})',
                        'availability_status': availability_status,
                        'is_available': is_available
                    })
                    logger.warning(f"Skipping unavailable item: {item.get('name')} (status: {availability_status})")

            if not available_items:
                return {
                    'success': False,
                    'error': 'No available items to add to cart',
                    'added_items': [],
                    'failed_items': unavailable_items
                }

            logger.info(f"Found {len(available_items)} available items out of {len(cart_items)} total items")

            # Step 2: Fetch fresh item data from store menu (group order UUIDs don't work for personal carts)
            # Get store slug for browser scraping fallback
            store_slug = _get_store_slug_from_uuid(store_uuid)

            fresh_items = []
            for item in available_items:
                fresh_item_data = await _fetch_fresh_item_data(session, store_uuid, item.get('name', ''), store_slug)

                if fresh_item_data:
                    # Merge original quantity and customizations with fresh data
                    fresh_item_data.update({
                        'quantity': item.get('quantity', 1),
                        'special_instructions': item.get('special_instructions', ''),
                        'original_price': item.get('price', 0)  # Keep original price for reference
                    })
                    fresh_items.append(fresh_item_data)
                    logger.info(f"✅ Found fresh data for: {item.get('name')}")
                else:
                    logger.warning(f"❌ Could not find fresh data for: {item.get('name')}")

            if not fresh_items:
                return {
                    'success': False,
                    'error': 'No items could be found in the current store menu',
                    'added_items': [],
                    'failed_items': [{'name': item.get('name'), 'error': 'Item not found in current menu'} for item in available_items]
                }

            logger.info(f"Successfully fetched fresh data for {len(fresh_items)} items")

            # Step 3: Create draft order with first fresh item
            first_item = fresh_items[0]
            delivery_address = cart_data.get('delivery_address', {})
            draft_result = await _create_draft_order_with_first_item(session, store_uuid, first_item, user_uuid, delivery_address)

            if not draft_result.get('success'):
                return {
                    'success': False,
                    'error': f"Failed to create draft order: {draft_result.get('error')}"
                }

            draft_order_uuid = draft_result.get('draft_order_uuid')
            cart_uuid = draft_result.get('cart_uuid')

            logger.info(f"Created draft order: {draft_order_uuid} with cart: {cart_uuid}")

            # Step 4: Add remaining fresh items to the draft order
            added_items = [{
                'name': first_item.get('title', 'Unknown'),
                'quantity': first_item.get('quantity', 1),
                'item_uuid': first_item.get('uuid', '')
            }]
            failed_items = list(unavailable_items)  # Start with unavailable items

            # Add remaining fresh items if any
            if len(fresh_items) > 1:
                for item in fresh_items[1:]:
                    item_result = await _add_item_to_draft_order(
                        session,
                        draft_order_uuid,
                        cart_uuid,
                        store_uuid,
                        item
                    )

                    if item_result.get('success'):
                        added_items.append({
                            'name': item.get('name', 'Unknown'),
                            'quantity': item.get('quantity', 1),
                            'item_uuid': item.get('product_id', '')
                        })
                        logger.info(f"Successfully added: {item.get('name', 'Unknown')} x{item.get('quantity', 1)}")
                    else:
                        failed_items.append({
                            'name': item.get('name', 'Unknown'),
                            'error': item_result.get('error', 'Unknown error')
                        })
                        logger.warning(f"Failed to add: {item.get('name', 'Unknown')} - {item_result.get('error')}")

            # Step 3: Perform checkout simulation if requested
            checkout_simulation = {}
            if include_checkout_simulation:
                logger.info("Performing checkout simulation for comprehensive pricing")
                simulation_result = await simulate_checkout(
                    draft_order_uuid,
                    cart_uuid,
                    store_uuid,
                    personal_cookies
                )

                if simulation_result.get('success'):
                    checkout_simulation = {
                        'success': True,
                        'comprehensive_pricing': simulation_result.get('pricing_breakdown', {}),
                        'currency_info': simulation_result.get('currency_info', {}),
                        'total_savings': simulation_result.get('total_savings', 0.0),
                        'simulation_method': simulation_result.get('simulation_method', 'getCheckoutPresentationV1')
                    }
                    logger.info(f"Checkout simulation successful - Total: {simulation_result.get('currency_info', {}).get('symbol', '$')}{simulation_result.get('pricing_breakdown', {}).get('total', 0):.2f}")
                else:
                    checkout_simulation = {
                        'success': False,
                        'error': simulation_result.get('error', 'Checkout simulation failed'),
                        'comprehensive_pricing': {},
                        'currency_info': {'is_cad': False, 'symbol': '$', 'code': 'USD'},
                        'total_savings': 0.0
                    }
                    logger.warning(f"Checkout simulation failed: {simulation_result.get('error')}")

            return {
                'success': True,
                'method': 'create_draft_then_add_items',
                'draft_order_uuid': draft_order_uuid,
                'personal_cart_uuid': cart_uuid,
                'store_uuid': store_uuid,
                'added_items': added_items,
                'failed_items': failed_items,
                'items_added_count': len(added_items),
                'items_failed_count': len(failed_items),
                'cart_url': f"https://www.ubereats.com/checkout?draftOrderUuid={draft_order_uuid}",
                'checkout_simulation': checkout_simulation
            }

        finally:
            await session.close()

    except Exception as e:
        logger.error(f"Error adding items to personal cart: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


def _parse_comprehensive_checkout_pricing(response_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse comprehensive pricing from getCheckoutPresentationV1 response using simplified approach.

    This function extracts only the essential pricing components:
    - Total amount (final checkout total)
    - Subtotal amount (items subtotal)
    - Calculates "Taxes & Other Fees" by subtraction: Total - Subtotal

    Args:
        response_data: Full API response from getCheckoutPresentationV1

    Returns:
        Dictionary with simplified pricing breakdown
    """
    try:
        # Initialize pricing structure with simplified approach
        pricing_breakdown = {
            'subtotal': 0.0,
            'taxes': 0.0,  # This will be calculated as Total - Subtotal
            'total': 0.0
        }

        # Navigate to checkout payloads - the real API structure
        data_section = response_data.get('data', {})
        checkout_payloads = data_section.get('checkoutPayloads', {})

        logger.debug(f"Checkout payloads keys: {list(checkout_payloads.keys())}")

        # Extract pricing components
        total_info = checkout_payloads.get('total', {})
        subtotal_info = checkout_payloads.get('subtotal', {})

        # Extract main pricing components - prioritize formattedValue over amountE5
        # The formattedValue contains the actual user-facing amounts with all discounts applied

        # Total: Try formattedValue first, then amountE5 as fallback
        total_info = checkout_payloads.get('total', {})
        if total_info and isinstance(total_info, dict):
            total_nested = total_info.get('total', {})
            if total_nested and isinstance(total_nested, dict):
                # Try formattedValue first (this is what users actually see)
                formatted_value = total_nested.get('formattedValue', '')
                if formatted_value:
                    try:
                        pricing_breakdown['total'] = float(formatted_value.replace('$', '').replace(',', ''))
                        logger.debug(f"Extracted total from formattedValue: ${pricing_breakdown['total']:.2f}")
                    except Exception as e:
                        logger.debug(f"Failed to parse formattedValue: {e}")
                        pass

                # Fallback to amountE5 if formattedValue failed
                if pricing_breakdown['total'] == 0.0:
                    total_value = total_nested.get('value', {})
                    if total_value and isinstance(total_value, dict):
                        amount_e5 = total_value.get('amountE5', 0)
                        pricing_breakdown['total'] = amount_e5 / 100000 if amount_e5 else 0.0
                        logger.debug(f"Extracted total from amountE5 fallback: ${pricing_breakdown['total']:.2f}")

        # Subtotal: Try formattedValue first, then amountE5 as fallback
        subtotal_info = checkout_payloads.get('subtotal', {})
        if subtotal_info and isinstance(subtotal_info, dict):
            subtotal_nested = subtotal_info.get('subtotal', {})
            if subtotal_nested and isinstance(subtotal_nested, dict):
                # Try formattedValue first (this is what users actually see)
                formatted_value = subtotal_nested.get('formattedValue', '')
                if formatted_value:
                    try:
                        pricing_breakdown['subtotal'] = float(formatted_value.replace('$', '').replace(',', ''))
                        logger.debug(f"Extracted subtotal from formattedValue: ${pricing_breakdown['subtotal']:.2f}")
                    except Exception as e:
                        logger.debug(f"Failed to parse formattedValue: {e}")
                        pass

                # Fallback to amountE5 if formattedValue failed
                if pricing_breakdown['subtotal'] == 0.0:
                    subtotal_value = subtotal_nested.get('value', {})
                    if subtotal_value and isinstance(subtotal_value, dict):
                        amount_e5 = subtotal_value.get('amountE5', 0)
                        pricing_breakdown['subtotal'] = amount_e5 / 100000 if amount_e5 else 0.0
                        logger.debug(f"Extracted subtotal from amountE5 fallback: ${pricing_breakdown['subtotal']:.2f}")

        # Calculate "Taxes & Other Fees" by simple subtraction
        # Since we're using your Uber One account, API returns correct discounted values
        if pricing_breakdown['total'] > 0 and pricing_breakdown['subtotal'] > 0:
            pricing_breakdown['taxes'] = pricing_breakdown['total'] - pricing_breakdown['subtotal']
            logger.debug(f"Calculated taxes & other fees: ${pricing_breakdown['taxes']:.2f}")

        # No estimation needed - using direct API values from Uber One account
        estimation_used = False
        raw_api_total = pricing_breakdown['total']

        logger.info(f"Direct API pricing (Uber One account) - Subtotal: ${pricing_breakdown['subtotal']:.2f}, Taxes & Other Fees: ${pricing_breakdown['taxes']:.2f}, Total: ${pricing_breakdown['total']:.2f}")

        return {
            'pricing_breakdown': pricing_breakdown,
            'currency_info': {'is_cad': False, 'symbol': '$', 'code': 'USD'},
            'total_savings': 0.0,
            'estimation_used': estimation_used,
            'raw_api_total': raw_api_total
        }

    except Exception as e:
        logger.error(f"Error parsing simplified checkout pricing: {str(e)}")
        return {
            'pricing_breakdown': {
                'subtotal': 0.0,
                'taxes': 0.0,
                'total': 0.0
            },
            'currency_info': {'is_cad': False, 'symbol': '$', 'code': 'USD'},
            'total_savings': 0.0
        }


def _extract_amount_from_payload(payload_data: Dict[str, Any]) -> float:
    """
    Extract monetary amount from various payload formats, including Uber's amountE5 format.

    Args:
        payload_data: Payload data that may contain amount information

    Returns:
        Float amount in dollars
    """
    try:
        if not payload_data:
            return 0.0

        # Handle Uber's specific amountE5 format (amount × 100000)
        if 'value' in payload_data and isinstance(payload_data['value'], dict):
            value_obj = payload_data['value']
            if 'amountE5' in value_obj:
                amount_e5 = value_obj['amountE5']
                return float(amount_e5) / 100000.0

        # Handle direct amountE5 field
        if 'amountE5' in payload_data:
            return float(payload_data['amountE5']) / 100000.0

        # Handle formattedValue (like "$30.38")
        if 'formattedValue' in payload_data:
            formatted_value = payload_data['formattedValue']
            try:
                return float(formatted_value.replace('$', '').replace(',', '').replace('CA$', ''))
            except:
                pass

        # Handle text field (like "$7.76" from charge values)
        if 'text' in payload_data:
            text_value = payload_data['text']
            try:
                # Remove currency symbols and parse
                clean_value = text_value.replace('$', '').replace(',', '').replace('CA$', '').strip()
                return float(clean_value)
            except:
                pass

        # Try different possible amount fields
        amount_fields = ['amount', 'value', 'total', 'price', 'cost', 'fee', 'subtotal']

        for field in amount_fields:
            if field in payload_data:
                amount = payload_data[field]

                # Handle different amount formats
                if isinstance(amount, (int, float)):
                    # If amount is > 100, likely in cents, convert to dollars
                    if amount > 100:
                        return amount / 100.0
                    return float(amount)

                elif isinstance(amount, str):
                    # Try to parse string amount
                    try:
                        parsed_amount = float(amount.replace('$', '').replace(',', '').replace('CA$', ''))
                        return parsed_amount
                    except:
                        continue

                elif isinstance(amount, dict):
                    # Nested amount object - recurse
                    nested_amount = _extract_amount_from_payload(amount)
                    if nested_amount > 0:
                        return nested_amount

        # Try to find amount in nested structures
        nested_fields = ['money', 'pricing', 'charge', 'fee']
        for field in nested_fields:
            if field in payload_data and isinstance(payload_data[field], dict):
                nested_amount = _extract_amount_from_payload(payload_data[field])
                if nested_amount > 0:
                    return nested_amount

        return 0.0

    except Exception as e:
        logger.debug(f"Error extracting amount from payload: {str(e)}")
        return 0.0


def _extract_discounted_amount(text_format: str) -> float:
    """
    Extract the discounted amount from HTML formatted text.

    Example: '<span><span style="text-decoration:line-through;">$1.49</span> $0.00</span>'
    Should return 0.00 (the final discounted amount)

    Args:
        text_format: HTML formatted text containing discount information

    Returns:
        Float amount of the discounted price, or None if not found
    """
    try:
        import re

        # Look for the pattern where there's a line-through price followed by the actual price
        # Pattern: line-through price, then final price

        # First, try to find the final price (usually after the line-through)
        # Look for $ amounts that are NOT in line-through spans

        # Remove line-through sections first
        without_strikethrough = re.sub(r'<span[^>]*text-decoration:line-through[^>]*>.*?</span>', '', text_format)

        # Now find dollar amounts in the remaining text
        dollar_matches = re.findall(r'\$(\d+\.?\d*)', without_strikethrough)

        if dollar_matches:
            # Take the last dollar amount found (usually the final price)
            final_price = float(dollar_matches[-1])
            logger.info(f"    Extracted discounted amount: ${final_price:.2f} from: {text_format[:100]}...")
            return final_price

        return None

    except Exception as e:
        logger.error(f"Error extracting discounted amount: {str(e)}")
        return None


def _detect_currency_from_payload(payload_data: Dict[str, Any], current_currency: Dict[str, Any]) -> Dict[str, Any]:
    """
    Detect currency information from payload data, including Uber's specific formats.

    Args:
        payload_data: Payload data that may contain currency information
        current_currency: Current currency info to update

    Returns:
        Updated currency information
    """
    try:
        # Check for currency in value object (Uber's amountE5 format)
        if 'value' in payload_data and isinstance(payload_data['value'], dict):
            value_obj = payload_data['value']
            if 'currencyCode' in value_obj:
                currency_code = value_obj['currencyCode']

                if currency_code == 'CAD':
                    current_currency['is_cad'] = True
                    current_currency['symbol'] = 'CA$'
                    current_currency['code'] = 'CAD'
                    return current_currency
                elif currency_code == 'USD':
                    current_currency['is_cad'] = False
                    current_currency['symbol'] = '$'
                    current_currency['code'] = 'USD'
                    return current_currency

        # Look for currency code in various fields
        currency_fields = ['currencyCode', 'currency', 'code']

        for field in currency_fields:
            if field in payload_data:
                currency_code = payload_data[field]

                if currency_code == 'CAD':
                    current_currency['is_cad'] = True
                    current_currency['symbol'] = 'CA$'
                    current_currency['code'] = 'CAD'
                    return current_currency
                elif currency_code == 'USD':
                    current_currency['is_cad'] = False
                    current_currency['symbol'] = '$'
                    current_currency['code'] = 'USD'
                    return current_currency

        # Look for currency symbol in text fields
        text_fields = ['text', 'displayText', 'formattedAmount', 'formattedValue']

        for field in text_fields:
            if field in payload_data:
                text = str(payload_data[field])

                if 'CA$' in text or 'CAD' in text:
                    current_currency['is_cad'] = True
                    current_currency['symbol'] = 'CA$'
                    current_currency['code'] = 'CAD'
                    return current_currency
                elif '$' in text and 'CA$' not in text:
                    # Default to USD for $ symbol
                    current_currency['is_cad'] = False
                    current_currency['symbol'] = '$'
                    current_currency['code'] = 'USD'
                    return current_currency

        return current_currency

    except Exception as e:
        logger.debug(f"Error detecting currency from payload: {str(e)}")
        return current_currency


def _parse_fare_breakdown(fare_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse fare breakdown data to extract individual fee components.

    Args:
        fare_data: Fare breakdown payload data

    Returns:
        Dictionary with parsed fee components
    """
    try:
        fees = {}

        # Parse the actual API structure with 'charges' array
        if 'charges' in fare_data:
            charges = fare_data['charges']
            logger.debug(f"Processing {len(charges)} charges from fareBreakdown")

            if isinstance(charges, list):
                for charge in charges:
                    # Get the title text to identify the fee type
                    title_text = charge.get('title', {}).get('text', '').lower()

                    # Extract amount from the charge value
                    amount = 0.0
                    discounted_amount = None

                    if 'chargeValue' in charge and 'badgeChargeValue' in charge['chargeValue']:
                        badge_value = charge['chargeValue']['badgeChargeValue']
                        amount = _extract_amount_from_payload(badge_value)

                        # Check for discount information in textFormat
                        text_format = badge_value.get('textFormat', '')
                        if 'line-through' in text_format and '$' in text_format:
                            # Extract discounted amount from formatted text
                            discounted_amount = _extract_discounted_amount(text_format)

                    elif 'value' in charge:
                        amount = _extract_amount_from_payload(charge['value'])

                        # Check for discount information in textFormat
                        text_format = charge['value'].get('textFormat', '')
                        if 'line-through' in text_format and '$' in text_format:
                            discounted_amount = _extract_discounted_amount(text_format)

                    # Use discounted amount if available
                    final_amount = discounted_amount if discounted_amount is not None else amount

                    logger.debug(f"Processing charge: '{title_text}' = ${amount:.2f}" +
                              (f" (discounted to ${final_amount:.2f})" if discounted_amount is not None else ""))

                    # Map charge titles to our standard fee names
                    if 'subtotal' in title_text:
                        # Skip subtotal as it's handled separately
                        continue
                    elif 'delivery' in title_text and 'fee' in title_text:
                        fees['delivery_fee'] = final_amount
                    elif 'service' in title_text and 'fee' in title_text:
                        fees['service_fee'] = final_amount
                    elif 'taxes' in title_text and 'other' in title_text and 'fees' in title_text:
                        fees['taxes'] = final_amount
                    elif 'tax' in title_text:
                        fees['taxes'] = final_amount
                    elif 'tip' in title_text:
                        fees['tips'] = final_amount
                    elif 'surge' in title_text:
                        fees['surge_fee'] = final_amount
                    elif 'small' in title_text and 'order' in title_text:
                        fees['small_order_fee'] = final_amount
                    elif 'regulatory' in title_text:
                        fees['regulatory_fee'] = final_amount
                    elif 'driver' in title_text and 'benefit' in title_text:
                        fees['ca_driver_benefit'] = final_amount
                    else:
                        # For unrecognized fees, add them as "other_fees"
                        if 'other_fees' not in fees:
                            fees['other_fees'] = 0.0
                        fees['other_fees'] += final_amount
                        logger.info(f"    Added to other_fees: ${final_amount:.2f}")

        # Legacy structure support (for backward compatibility)
        elif 'breakdown' in fare_data:
            breakdown = fare_data['breakdown']

            if isinstance(breakdown, list):
                for item in breakdown:
                    fee_type = item.get('type', '').lower()
                    amount = _extract_amount_from_payload(item)

                    # Map fee types to our standard names
                    if 'delivery' in fee_type:
                        fees['delivery_fee'] = amount
                    elif 'service' in fee_type:
                        fees['service_fee'] = amount
                    elif 'tax' in fee_type:
                        fees['taxes'] = amount
                    elif 'tip' in fee_type:
                        fees['tips'] = amount
                    elif 'surge' in fee_type:
                        fees['surge_fee'] = amount
                    elif 'small' in fee_type and 'order' in fee_type:
                        fees['small_order_fee'] = amount
                    elif 'regulatory' in fee_type:
                        fees['regulatory_fee'] = amount
                    elif 'driver' in fee_type and 'benefit' in fee_type:
                        if 'ca' in fee_type.lower():
                            fees['ca_driver_benefit'] = amount
                        else:
                            fees['driver_benefit'] = amount

        # Direct fee fields
        fee_mappings = {
            'deliveryFee': 'delivery_fee',
            'serviceFee': 'service_fee',
            'tax': 'taxes',
            'taxes': 'taxes',
            'tip': 'tips',
            'surge': 'surge_fee',
            'smallOrderFee': 'small_order_fee',
            'regulatoryFee': 'regulatory_fee',
            'driverBenefit': 'driver_benefit'
        }

        for api_field, fee_key in fee_mappings.items():
            if api_field in fare_data:
                fees[fee_key] = _extract_amount_from_payload(fare_data[api_field])

        return fees

    except Exception as e:
        logger.debug(f"Error parsing fare breakdown: {str(e)}")
        return {}


def _parse_promotions(promotion_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Parse promotion data to extract discount information.

    Args:
        promotion_data: Promotion payload data

    Returns:
        Dictionary with parsed promotion discounts
    """
    try:
        promotions = {}
        total_promotion_savings = 0.0

        # Handle different promotion structures
        if 'promotions' in promotion_data:
            promo_list = promotion_data['promotions']

            if isinstance(promo_list, list):
                for promo in promo_list:
                    promo_type = promo.get('type', '').lower()
                    discount_amount = _extract_amount_from_payload(promo.get('discount', {}))

                    total_promotion_savings += discount_amount

                    # Categorize promotion types
                    if 'uber' in promo_type and 'one' in promo_type:
                        promotions['uber_one_discount'] = discount_amount
                    elif 'membership' in promo_type:
                        promotions['membership_discount'] = discount_amount
                    elif 'promo' in promo_type or 'code' in promo_type:
                        promotions['promo_code_discount'] = discount_amount
                    elif 'voucher' in promo_type:
                        promotions['voucher_discount'] = discount_amount
                    elif 'loyalty' in promo_type:
                        promotions['loyalty_points_discount'] = discount_amount
                    else:
                        promotions['promotion_discount'] = promotions.get('promotion_discount', 0.0) + discount_amount

        # Direct promotion fields
        if 'totalDiscount' in promotion_data:
            total_promotion_savings = _extract_amount_from_payload(promotion_data['totalDiscount'])

        promotions['total_promotion_savings'] = total_promotion_savings

        return promotions

    except Exception as e:
        logger.debug(f"Error parsing promotions: {str(e)}")
        return {}


def _generate_shopping_cart_item_uuid() -> str:
    """Generate a UUID for shopping cart items."""
    import uuid
    return str(uuid.uuid4())


def _convert_extracted_item_to_shopping_cart_format(item: Dict[str, Any], store_uuid: str) -> Dict[str, Any]:
    """
    Convert extracted item data to the shopping cart format required by Uber Eats API.

    Args:
        item: Item data from extract_cart_info()
        store_uuid: Store UUID

    Returns:
        Item in shopping cart format
    """
    try:
        # Generate required UUIDs
        shopping_cart_item_uuid = _generate_shopping_cart_item_uuid()

        # Price is already in cents from fresh item data, no conversion needed
        price_cents = int(item.get('price', 0))

        # Temporarily disable customizations to isolate the core API issue
        # The working example has complex customizations, but let's test without them first
        customizations = {}

        # Build the shopping cart item using fresh item data with complete structure
        shopping_cart_item = {
            "uuid": item.get('uuid', '') or item.get('product_id', ''),  # Fresh data uses 'uuid', extracted uses 'product_id'
            "shoppingCartItemUuid": shopping_cart_item_uuid,
            "storeUuid": store_uuid,
            "sectionUuid": item.get('section_uuid', ''),  # Use actual section UUID from data
            "subsectionUuid": item.get('subsection_uuid', ''),  # Use actual subsection UUID from data
            "price": price_cents,
            "title": item.get('title', '') or item.get('name', ''),  # Fresh data uses 'title', extracted uses 'name'
            "quantity": item.get('quantity', 1),
            "customizations": customizations,
            "imageURL": item.get('image_url', ''),  # Use actual image URL from data
            "specialInstructions": item.get('special_instructions', ''),
            "itemId": item.get('item_id', None),  # Use actual item ID if available
            # Additional fields that might be required
            "isAvailable": item.get('is_available', True),
            "availabilityStatus": item.get('availability_status', 'AVAILABLE'),
            "catalogSectionUuid": item.get('section_uuid', ''),  # Some APIs might expect this
            "catalogSubsectionUuid": item.get('subsection_uuid', ''),  # Some APIs might expect this
        }

        return shopping_cart_item

    except Exception as e:
        logger.error(f"Error converting item to shopping cart format: {str(e)}")
        return {}


async def _create_draft_order_with_first_item(session: aiohttp.ClientSession, store_uuid: str, item: Dict[str, Any], user_uuid: str, delivery_address: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Create a draft order with the first item using createDraftOrderV2.

    Args:
        session: Authenticated personal account session
        store_uuid: Store UUID
        item: First item to add
        user_uuid: User UUID

    Returns:
        Dictionary with draft order UUID and cart UUID
    """
    try:
        # Convert item to shopping cart format
        shopping_cart_item = _convert_extracted_item_to_shopping_cart_format(item, store_uuid)

        if not shopping_cart_item:
            return {
                'success': False,
                'error': 'Failed to convert item to shopping cart format'
            }

        # Build the createDraftOrderV2 payload based on the real format from your captured request
        create_payload = {
            "isMulticart": True,
            "shoppingCartItems": [shopping_cart_item],
            "useCredits": True,
            "extraPaymentProfiles": [],
            "promotionOptions": {
                "autoApplyPromotionUUIDs": [],
                "selectedPromotionInstanceUUIDs": [],
                "skipApplyingPromotion": False
            },
            "deliveryTime": {
                "asap": True
            },
            "deliveryType": "ASAP",
            "currencyCode": "USD",  # TODO: Detect from cart data
            "interactionType": "door_to_door",
            "paymentProfileUUID": "a1ca6530-e5e3-51e7-b44e-ab0605972fa3",  # Required field
            "checkMultipleDraftOrdersCap": True,
            "actionMeta": {
                "isQuickAdd": False,
                "numClicks": 1
            },
            "businessDetails": {}
        }

        # CRITICAL FIX: Add delivery address directly to the payload if provided
        if delivery_address and isinstance(delivery_address, dict):
            # Use the raw delivery address structure from group order extraction
            raw_delivery_address = delivery_address.get('raw_delivery_address', {})
            if raw_delivery_address:
                create_payload["deliveryAddress"] = raw_delivery_address
                logger.info(f"✅ Added delivery address to createDraftOrderV2 payload: {delivery_address.get('address1', '')}, {delivery_address.get('city', '')}, {delivery_address.get('state', '')}")
            else:
                # Fallback: construct delivery address from parsed fields
                create_payload["deliveryAddress"] = {
                    "latitude": delivery_address.get('latitude', 0.0),
                    "longitude": delivery_address.get('longitude', 0.0),
                    "address": {
                        "address1": delivery_address.get('address1', ''),
                        "address2": delivery_address.get('address2', ''),
                        "aptOrSuite": "",
                        "eaterFormattedAddress": delivery_address.get('subtitle', ''),
                        "title": delivery_address.get('formatted_address', delivery_address.get('address1', '')),
                        "subtitle": delivery_address.get('subtitle', ''),
                        "uuid": ""
                    },
                    "reference": delivery_address.get('reference', ''),
                    "referenceType": delivery_address.get('reference_type', 'uber_places'),
                    "type": delivery_address.get('reference_type', 'uber_places'),
                    "addressComponents": {
                        "city": delivery_address.get('city', ''),
                        "countryCode": delivery_address.get('country', 'US'),
                        "firstLevelSubdivisionCode": delivery_address.get('state', ''),
                        "postalCode": delivery_address.get('zipcode', '')
                    }
                }
                logger.info(f"✅ Constructed delivery address for createDraftOrderV2 payload: {delivery_address.get('address1', '')}, {delivery_address.get('city', '')}, {delivery_address.get('state', '')}")
        else:
            logger.warning("⚠️  No delivery address provided to createDraftOrderV2 - may use account default")

        logger.debug(f"Creating draft order with payload: {json.dumps(create_payload, indent=2)}")

        # Make the API call
        result = await make_api_request(session, "createDraftOrderV2", create_payload, use_personal_headers=True)

        if result.get('success'):
            response_data = result.get('data', {})
            data_section = response_data.get('data', {})

            # Extract UUIDs from the correct locations based on the actual API response
            draft_order_data = data_section.get('draftOrder', {})
            draft_order_uuid = draft_order_data.get('uuid')

            shopping_cart_data = draft_order_data.get('shoppingCart', {})
            cart_uuid = shopping_cart_data.get('cartUuid')

            if draft_order_uuid and cart_uuid:
                logger.info(f"Successfully created draft order: {draft_order_uuid}")
                logger.info(f"Cart UUID: {cart_uuid}")
                return {
                    'success': True,
                    'draft_order_uuid': draft_order_uuid,
                    'cart_uuid': cart_uuid
                }
            else:
                logger.error(f"Missing UUIDs - Draft: {draft_order_uuid}, Cart: {cart_uuid}")
                logger.debug(f"Available data keys: {list(data_section.keys())}")
                return {
                    'success': False,
                    'error': 'No draft order UUID or cart UUID in response'
                }
        else:
            error_data = result.get('data', {})
            error_message = error_data.get('message', 'Unknown error')
            logger.error(f"Failed to create draft order: {error_message}")

            return {
                'success': False,
                'error': error_message
            }

    except Exception as e:
        logger.error(f"Error creating draft order: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }


async def _add_item_to_draft_order(session: aiohttp.ClientSession, draft_order_uuid: str, cart_uuid: str, store_uuid: str, item: Dict[str, Any]) -> Dict[str, Any]:
    """
    Add an item to existing draft order using addItemsToDraftOrderV2.

    Args:
        session: Authenticated personal account session
        draft_order_uuid: Draft order UUID
        cart_uuid: Cart UUID
        store_uuid: Store UUID
        item: Item to add

    Returns:
        Dictionary with success status
    """
    try:
        # Convert item to the format required by addItemsToDraftOrderV2
        shopping_cart_item = _convert_extracted_item_to_shopping_cart_format(item, store_uuid)

        if not shopping_cart_item:
            return {
                'success': False,
                'error': 'Failed to convert item to shopping cart format'
            }

        # Build the addItemsToDraftOrderV2 payload based on the real format
        add_payload = {
            "draftOrderUUID": draft_order_uuid,
            "cartUUID": cart_uuid,
            "items": [shopping_cart_item],
            "shouldUpdateDraftOrderMetadata": False,
            "storeUUID": store_uuid,
            "actionMeta": {
                "isQuickAdd": False,
                "numClicks": 1
            }
        }

        logger.debug(f"Adding item to draft order with payload: {json.dumps(add_payload, indent=2)}")

        # Make the API call
        result = await make_api_request(session, "addItemsToDraftOrderV2", add_payload, use_personal_headers=True)

        if result.get('success'):
            logger.info(f"Successfully added item to draft order: {item.get('name', 'Unknown')}")
            return {
                'success': True,
                'item_uuid': item.get('product_id', '')
            }
        else:
            error_data = result.get('data', {})
            error_message = error_data.get('message', 'Unknown error')
            logger.error(f"Failed to add item to draft order: {error_message}")

            return {
                'success': False,
                'error': error_message
            }

    except Exception as e:
        logger.error(f"Error adding item to draft order: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }





async def rebuild_cart(cart_data: Dict[str, Any], session: Optional[aiohttp.ClientSession] = None) -> Dict[str, Any]:
    """
    Rebuild cart with fresh pricing data using createDraftOrderV2 endpoint.

    This function recreates the cart with the same items, quantities, and customizations
    but with updated pricing information from the API.

    Args:
        cart_data: Cart information extracted from extract_cart_info()
        session: Optional existing aiohttp session

    Returns:
        Dictionary containing rebuilt cart with fresh pricing data
    """
    session_created = False

    try:
        # Create session if not provided
        if session is None:
            cookies = parse_cookies(DEFAULT_COOKIE_STRING)
            session = aiohttp.ClientSession(cookies=cookies)
            session_created = True

        if not cart_data.get('success'):
            logger.error("Cannot rebuild cart from failed extraction")
            return {'success': False, 'error': 'Invalid cart data provided'}

        logger.info("Starting cart reconstruction with fresh pricing data")

        # Prepare cart items for reconstruction
        reconstructed_items = []
        for item in cart_data.get('cart_items', []):
            reconstructed_item = {
                'productId': item.get('product_id', ''),
                'quantity': item.get('quantity', 1),
                'specialInstructions': item.get('special_instructions', ''),
                'customizations': []
            }

            # Rebuild customizations
            for customization in item.get('customizations', []):
                custom_data = {
                    'customizationId': customization.get('customization_id', ''),
                    'selectedOptions': []
                }

                # Add selected options
                for option in customization.get('options', []):
                    if option.get('selected', False):
                        custom_data['selectedOptions'].append({
                            'optionId': option.get('option_id', ''),
                            'quantity': 1
                        })

                if custom_data['selectedOptions']:
                    reconstructed_item['customizations'].append(custom_data)

            reconstructed_items.append(reconstructed_item)

        # Get store and delivery information
        store_info = cart_data.get('store_info', {})
        delivery_address = cart_data.get('delivery_address', {})

        # Create draft order payload
        draft_payload = {
            "isMulticart": True,
            "currencyCode": "CAD" if cart_data.get('currency_info', {}).get('is_cad') else "USD",
            "deliveryType": "ASAP",
            "deliveryTime": {
                "asap": True
            },
            "interactionType": "door_to_door",
            "storeUuid": store_info.get('store_uuid', ''),
            "cartItems": reconstructed_items,
            "deliveryLocation": {
                "latitude": delivery_address.get('latitude', 0.0),
                "longitude": delivery_address.get('longitude', 0.0),
                "address": {
                    "address1": delivery_address.get('address1', ''),
                    "address2": delivery_address.get('address2', ''),
                    "city": delivery_address.get('city', ''),
                    "state": delivery_address.get('state', ''),
                    "zipcode": delivery_address.get('zipcode', ''),
                    "country": delivery_address.get('country', 'US')
                }
            }
        }

        # Create new draft order
        result = await make_api_request(session, "createDraftOrderV2", draft_payload)

        if not result.get('success'):
            logger.error("Failed to create new draft order")
            return {
                'success': False,
                'error': 'Failed to create new draft order',
                'api_response': result
            }

        # Extract new group UUID from response
        response_data = result.get('data', {})
        new_group_uuid = response_data.get('data', {}).get('draftOrderUuid')

        if not new_group_uuid:
            logger.error("No group UUID returned from new draft order")
            return {
                'success': False,
                'error': 'No group UUID in response'
            }

        logger.info(f"Successfully created new draft order: {new_group_uuid}")

        # Now extract fresh pricing from the new order
        fresh_cart_data = await extract_cart_info(new_group_uuid, session)

        if not fresh_cart_data.get('success'):
            logger.error("Failed to extract fresh pricing from rebuilt cart")
            return {
                'success': False,
                'error': 'Failed to get fresh pricing data'
            }

        return {
            'success': True,
            'new_group_uuid': new_group_uuid,
            'fresh_cart_data': fresh_cart_data,
            'original_cart_data': cart_data,
            'reconstruction_method': 'createDraftOrderV2'
        }

    except Exception as e:
        logger.error(f"Error rebuilding cart: {str(e)}")
        return {
            'success': False,
            'error': str(e)
        }

    finally:
        # Clean up session if we created it
        if session_created and session:
            await session.close()


async def compare_pricing(original_cart: Dict[str, Any], rebuilt_cart: Dict[str, Any]) -> Dict[str, Any]:
    """
    Compare pricing between original and rebuilt carts to identify changes.

    Args:
        original_cart: Original cart data from extract_cart_info()
        rebuilt_cart: Rebuilt cart data from rebuild_cart()

    Returns:
        Dictionary containing detailed pricing comparison
    """
    try:
        logger.info("Comparing pricing between original and rebuilt carts")

        # Extract pricing data
        original_pricing = original_cart.get('pricing_breakdown', {})
        rebuilt_pricing = rebuilt_cart.get('fresh_cart_data', {}).get('pricing_breakdown', {})

        # Calculate differences
        comparison = {
            'success': True,
            'currency_info': original_cart.get('currency_info', {}),
            'original_pricing': original_pricing,
            'rebuilt_pricing': rebuilt_pricing,
            'differences': {},
            'total_difference': 0.0,
            'percentage_change': 0.0,
            'items_comparison': [],
            'recommendations': []
        }

        # Compare key pricing components
        pricing_keys = ['subtotal', 'delivery_fee', 'service_fee', 'ca_driver_benefit',
                       'taxes', 'total_fees', 'uber_one_discount', 'total']

        for key in pricing_keys:
            original_value = original_pricing.get(key, 0.0)
            rebuilt_value = rebuilt_pricing.get(key, 0.0)
            difference = rebuilt_value - original_value

            comparison['differences'][key] = {
                'original': original_value,
                'rebuilt': rebuilt_value,
                'difference': difference,
                'percentage_change': ((difference / original_value) * 100) if original_value != 0 else 0.0
            }

        # Calculate overall changes
        original_total = original_pricing.get('total', 0.0)
        rebuilt_total = rebuilt_pricing.get('total', 0.0)
        comparison['total_difference'] = rebuilt_total - original_total
        comparison['percentage_change'] = ((comparison['total_difference'] / original_total) * 100) if original_total != 0 else 0.0

        # Generate recommendations
        if abs(comparison['total_difference']) > 0.01:  # More than 1 cent difference
            if comparison['total_difference'] > 0:
                comparison['recommendations'].append("Price has increased - consider checking for promotions")
            else:
                comparison['recommendations'].append("Price has decreased - good time to place order")
        else:
            comparison['recommendations'].append("Pricing is stable")

        # Compare item availability
        original_items = {item.get('product_id'): item for item in original_cart.get('cart_items', [])}
        rebuilt_items = {item.get('product_id'): item for item in rebuilt_cart.get('fresh_cart_data', {}).get('cart_items', [])}

        for product_id, original_item in original_items.items():
            if product_id in rebuilt_items:
                rebuilt_item = rebuilt_items[product_id]
                item_comparison = {
                    'product_id': product_id,
                    'name': original_item.get('name', ''),
                    'status': 'available',
                    'price_change': rebuilt_item.get('price', 0) - original_item.get('price', 0)
                }
            else:
                item_comparison = {
                    'product_id': product_id,
                    'name': original_item.get('name', ''),
                    'status': 'unavailable',
                    'price_change': 0.0
                }
                comparison['recommendations'].append(f"Item '{original_item.get('name', 'Unknown')}' is no longer available")

            comparison['items_comparison'].append(item_comparison)

        logger.info(f"Pricing comparison complete - Total difference: ${comparison['total_difference']:.2f}")
        return comparison

    except Exception as e:
        logger.error(f"Error comparing pricing: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'currency_info': {},
            'original_pricing': {},
            'rebuilt_pricing': {},
            'differences': {},
            'total_difference': 0.0,
            'percentage_change': 0.0,
            'items_comparison': [],
            'recommendations': []
        }


def set_personal_authentication(cookie_string: str) -> Dict[str, Any]:
    """
    Configure personal account authentication for cart operations.

    Args:
        cookie_string: Complete cookie string from your logged-in Uber Eats session

    Returns:
        Dictionary with configuration status
    """
    try:
        # Validate the cookie string
        cookies = parse_cookies(cookie_string)

        # Check for essential authentication cookies (based on captured request)
        required_cookies = ['jwt-session', 'uev2.id.session', 'uev2.loc', '_userUuid']
        missing_cookies = [cookie for cookie in required_cookies if cookie not in cookies]

        if missing_cookies:
            return {
                'success': False,
                'error': f'Missing required cookies: {", ".join(missing_cookies)}',
                'instructions': 'Please ensure you copy the complete cookie string from your logged-in session'
            }

        # Validate specific cookie formats
        if cookies.get('_userUuid') == 'undefined':
            return {
                'success': False,
                'error': '_userUuid is undefined - please ensure you are logged in',
                'instructions': 'Make sure you are logged into your Uber Eats account when copying cookies'
            }

        # Extract user UUID for validation
        user_uuid = extract_user_uuid_from_cookies(cookie_string)
        if not user_uuid:
            return {
                'success': False,
                'error': 'Could not extract user UUID from cookies',
                'instructions': 'Ensure you are logged in and copy cookies from an active session'
            }

        # Update global variable (in production, you'd save this securely)
        global PERSONAL_ACCOUNT_COOKIES
        PERSONAL_ACCOUNT_COOKIES = cookie_string

        return {
            'success': True,
            'user_uuid': user_uuid,
            'message': 'Personal authentication configured successfully',
            'cookies_count': len(cookies)
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }


async def add_group_order_to_personal_cart(group_link: str, personal_cookies: str = None, include_checkout_simulation: bool = True) -> Dict[str, Any]:
    """
    Extract items from a group order and add them to your personal Uber Eats cart.

    This is the main function for personal cart integration. It uses a two-step process:
    1. Creates a draft order with the first item using createDraftOrderV2
    2. Adds remaining items using addItemsToDraftOrderV2

    The function handles authentication, item conversion, and provides a cart URL
    for immediate checkout with current pricing.

    Args:
        group_link: Group order link to extract items from
        personal_cookies: Your personal account cookies (optional if already configured)
        include_checkout_simulation: Whether to perform checkout simulation for comprehensive pricing (default: True)

    Returns:
        Dictionary containing:
        - success: Boolean indicating if operation succeeded
        - draft_order_uuid: UUID of created draft order
        - personal_cart_uuid: UUID of personal cart
        - items_added: List of successfully added items
        - items_failed: List of items that failed to add
        - cart_url: Direct URL to checkout the cart
        - items_added_count: Number of items successfully added
        - items_failed_count: Number of items that failed
        - checkout_simulation: Comprehensive pricing data from checkout simulation
    """
    try:
        logger.info(f"Starting group order to personal cart transfer: {group_link}")

        # Use provided cookies or global configuration
        if personal_cookies:
            auth_cookies = personal_cookies
        else:
            auth_cookies = PERSONAL_ACCOUNT_COOKIES

        # Extract group UUID from link
        group_uuid = extract_group_uuid_from_link(group_link)

        # Step 1: Extract cart information from group order (with complete method)
        logger.info("Step 1: Extracting items from group order using complete method")

        # First try the complete extraction method (includes addMemberToDraftOrderV1)
        cart_data = await extract_complete_cart_info(group_uuid)

        # Fallback to basic extraction if complete method fails
        if not cart_data.get('success'):
            logger.warning("Complete extraction failed, trying fallback method")
            cart_data = await extract_cart_info(group_uuid)

        if not cart_data.get('success'):
            return {
                'success': False,
                'error': f"Failed to extract group order items: {cart_data.get('error')}",
                'step_failed': 'extraction'
            }

        # Step 2: Add items to personal cart
        logger.info("Step 2: Adding items to personal cart")
        add_result = await add_items_to_personal_cart(cart_data, auth_cookies, include_checkout_simulation)

        if not add_result.get('success'):
            return {
                'success': False,
                'error': f"Failed to add items to personal cart: {add_result.get('error')}",
                'step_failed': 'personal_cart_addition',
                'extracted_items': cart_data.get('cart_items', [])
            }

        # Success!
        return {
            'success': True,
            'group_link': group_link,
            'group_uuid': group_uuid,
            'draft_order_uuid': add_result.get('draft_order_uuid'),
            'personal_cart_uuid': add_result.get('personal_cart_uuid'),
            'store_uuid': add_result.get('store_uuid'),
            'items_added': add_result.get('added_items', []),
            'items_failed': add_result.get('failed_items', []),
            'items_added_count': add_result.get('items_added_count', 0),
            'items_failed_count': add_result.get('items_failed_count', 0),
            'cart_url': add_result.get('cart_url', ''),
            'checkout_simulation': add_result.get('checkout_simulation', {}),
            'timestamp': datetime.now().isoformat(),
            'original_cart_data': cart_data
        }

    except Exception as e:
        logger.error(f"Error in group order to personal cart transfer: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'group_link': group_link,
            'timestamp': datetime.now().isoformat()
        }


async def simulate_checkout(draft_order_uuid: str, cart_uuid: str = None, store_uuid: str = None, personal_cookies: str = None) -> Dict[str, Any]:
    """
    Simulate the checkout process to retrieve comprehensive pricing information.

    This function attempts to call various checkout API endpoints to get detailed
    pricing breakdown including all fees, taxes, discounts, and promotions without
    actually completing the purchase.

    NOTE: Currently the exact checkout endpoint is not identified, so this function
    includes multiple endpoint attempts and fallback methods. The core cart creation
    functionality works perfectly, but comprehensive pricing simulation needs the
    correct API endpoint to be discovered.

    Args:
        draft_order_uuid: Draft order UUID from add_group_order_to_personal_cart()
        cart_uuid: Cart UUID (optional, not used in current implementation)
        store_uuid: Store UUID (optional, not used in current implementation)
        personal_cookies: Personal account cookies (optional if already configured)

    Returns:
        Dictionary containing:
        - success: Boolean indicating if simulation succeeded
        - pricing_breakdown: Comprehensive pricing information (when available)
        - currency_info: Currency details (USD/CAD)
        - total_savings: Total amount saved from discounts
        - raw_response: Raw API response for debugging
        - error: Error message if simulation failed
        - simulation_method: Which method/endpoint was used successfully
    """
    try:
        # Use provided cookies or global configuration
        if personal_cookies:
            auth_cookies = personal_cookies
        else:
            auth_cookies = PERSONAL_ACCOUNT_COOKIES

        if not auth_cookies or auth_cookies.strip() == "REPLACE_WITH_YOUR_PERSONAL_ACCOUNT_COOKIES":
            return {
                'success': False,
                'error': 'Personal account cookies not configured for checkout simulation',
                'pricing_breakdown': {},
                'currency_info': {},
                'total_savings': 0.0,
                'raw_response': {}
            }

        logger.info(f"Starting checkout simulation for draft order: {draft_order_uuid}")

        # Parse personal account cookies
        cookies = parse_cookies(auth_cookies)
        session = aiohttp.ClientSession(cookies=cookies)

        try:
            # Build comprehensive checkout payload with the correct structure from your captured request
            checkout_payload = {
                "payloadTypes": [
                    "subtotal",
                    "canonicalProductStorePickerPayload",
                    "total",
                    "paymentProfilesEligibility",
                    "cartItems",
                    "basketSize",
                    "promotion",
                    "restrictedItems",
                    "venueSectionPicker",
                    "locationInfo",
                    "upsellCatalogSections",
                    "subTotalFareBreakdown",
                    "storeSwitcherActionableBannerPayload",
                    "fareBreakdown",
                    "promoAndMembershipSavingBannerPayload",
                    "passBanner",
                    "passBannerOnCartPayload",
                    "merchantMembership",
                    "requestUtensilPayload",
                    "cartPageHeader",
                    "fulfilledItemDetails",
                    "unFulfilledItemDetails",
                    "inReviewItemDetails",
                    "uneditableItemDetails",
                    "upsellFeed"
                ],
                "draftOrderUUID": draft_order_uuid,  # Note: UUID not Uuid
                "isGroupOrder": False,
                "clientFeaturesData": {
                    "paymentSelectionContext": {
                        "value": "{\"deviceContext\":{\"thirdPartyApplications\":[\"google_pay\"]}}"
                    }
                }
            }

            logger.debug(f"Checkout simulation payload: {json.dumps(checkout_payload, indent=2)}")

            # Make the API call with the correct endpoint and payload structure
            logger.debug(f"Making getCheckoutPresentationV1 call with payload: {json.dumps(checkout_payload, indent=2)}")
            result = await make_api_request(session, "getCheckoutPresentationV1", checkout_payload, use_personal_headers=True)

            successful_endpoint = "getCheckoutPresentationV1" if result.get('success') and result.get('data', {}).get('status') == 'success' else None

            if successful_endpoint and result.get('success'):
                response_data = result.get('data', {})

                # Parse the comprehensive pricing data
                pricing_result = _parse_comprehensive_checkout_pricing(response_data)

                logger.info(f"Checkout simulation successful - Total: {pricing_result.get('currency_info', {}).get('symbol', '$')}{pricing_result.get('pricing_breakdown', {}).get('total', 0):.2f}")

                return {
                    'success': True,
                    'pricing_breakdown': pricing_result.get('pricing_breakdown', {}),
                    'currency_info': pricing_result.get('currency_info', {}),
                    'total_savings': pricing_result.get('total_savings', 0.0),
                    'estimation_used': pricing_result.get('estimation_used', False),
                    'raw_api_total': pricing_result.get('raw_api_total'),
                    'raw_response': response_data,
                    'simulation_method': successful_endpoint
                }
            else:
                # Fallback: Try to get basic pricing from the draft order itself
                logger.info("Checkout endpoints failed, trying draft order fallback...")
                fallback_result = await _get_draft_order_pricing_fallback(session, draft_order_uuid)

                if fallback_result.get('success'):
                    logger.info(f"Draft order fallback successful - Total: {fallback_result.get('currency_info', {}).get('symbol', '$')}{fallback_result.get('pricing_breakdown', {}).get('total', 0):.2f}")
                    return fallback_result

                # Complete failure
                error_message = result.get('data', {}).get('message', 'All checkout simulation methods failed') if result else 'No successful API calls'

                logger.error(f"Checkout simulation completely failed: {error_message}")

                return {
                    'success': False,
                    'error': error_message,
                    'pricing_breakdown': {},
                    'currency_info': {'is_cad': False, 'symbol': '$', 'code': 'USD'},
                    'total_savings': 0.0,
                    'raw_response': result.get('data', {}) if result else {}
                }

        finally:
            await session.close()

    except Exception as e:
        logger.error(f"Error in checkout simulation: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'pricing_breakdown': {},
            'currency_info': {},
            'total_savings': 0.0,
            'raw_response': {}
        }


async def _get_draft_order_pricing_fallback(session: aiohttp.ClientSession, draft_order_uuid: str) -> Dict[str, Any]:
    """
    Fallback method to get basic pricing from draft order when checkout simulation fails.

    Args:
        session: Authenticated session
        draft_order_uuid: Draft order UUID

    Returns:
        Dictionary with basic pricing information
    """
    try:
        # Try to get draft order details
        draft_order_payload = {
            "draftOrderUuid": draft_order_uuid
        }

        # Try different endpoints for getting draft order info
        draft_endpoints = [
            "getDraftOrderV2",
            "getDraftOrder",
            "getDraftOrderDetails"
        ]

        for endpoint in draft_endpoints:
            result = await make_api_request(session, endpoint, draft_order_payload, use_personal_headers=True)

            if result.get('success') and result.get('data', {}).get('status') == 'success':
                response_data = result.get('data', {})
                data_section = response_data.get('data', {})

                # Try to extract basic pricing from draft order
                if 'draftOrder' in data_section:
                    draft_order = data_section['draftOrder']

                    # Look for pricing in the shopping cart
                    shopping_cart = draft_order.get('shoppingCart', {})

                    # Basic pricing extraction
                    pricing_breakdown = {
                        'subtotal': 0.0,
                        'delivery_fee': 0.0,
                        'service_fee': 0.0,
                        'taxes': 0.0,
                        'total': 0.0
                    }

                    # Try to get subtotal from items
                    items = shopping_cart.get('items', [])
                    subtotal = 0.0

                    for item in items:
                        item_price = item.get('price', 0)
                        quantity = item.get('quantity', 1)

                        # Convert from cents if needed
                        if item_price > 100:
                            item_price = item_price / 100.0

                        subtotal += item_price * quantity

                    pricing_breakdown['subtotal'] = subtotal
                    pricing_breakdown['total'] = subtotal  # Basic fallback

                    # Detect currency
                    currency_code = shopping_cart.get('currencyCode', 'USD')
                    currency_info = {
                        'is_cad': currency_code == 'CAD',
                        'symbol': 'CA$' if currency_code == 'CAD' else '$',
                        'code': currency_code
                    }

                    logger.info(f"Draft order fallback extracted basic pricing - Subtotal: {currency_info['symbol']}{subtotal:.2f}")

                    return {
                        'success': True,
                        'pricing_breakdown': pricing_breakdown,
                        'currency_info': currency_info,
                        'total_savings': 0.0,
                        'simulation_method': f'draft_order_fallback_{endpoint}'
                    }

        # Complete fallback failure
        return {
            'success': False,
            'error': 'Draft order pricing fallback failed',
            'pricing_breakdown': {},
            'currency_info': {'is_cad': False, 'symbol': '$', 'code': 'USD'},
            'total_savings': 0.0
        }

    except Exception as e:
        logger.error(f"Error in draft order pricing fallback: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'pricing_breakdown': {},
            'currency_info': {'is_cad': False, 'symbol': '$', 'code': 'USD'},
            'total_savings': 0.0
        }


async def check_group_order_pricing(group_link: str) -> Dict[str, Any]:
    """
    Main function to check and refresh pricing for a group order.

    This is the primary interface function that combines extraction, reconstruction,
    and comparison to provide comprehensive pricing analysis.

    Args:
        group_link: Full Uber Eats group order URL

    Returns:
        Dictionary containing complete pricing analysis and recommendations
    """
    try:
        logger.info(f"Starting comprehensive price check for: {group_link}")

        # Extract group UUID from link
        group_uuid = extract_group_uuid_from_link(group_link)

        # Create authenticated session
        cookies = parse_cookies(DEFAULT_COOKIE_STRING)
        session = aiohttp.ClientSession(cookies=cookies)

        try:
            # Step 1: Extract original cart information
            logger.info("Step 1: Extracting original cart information")
            original_cart = await extract_cart_info(group_uuid, session)

            if not original_cart.get('success'):
                return {
                    'success': False,
                    'error': f"Failed to extract original cart: {original_cart.get('error', 'Unknown error')}",
                    'step_failed': 'extraction'
                }

            # Step 2: Rebuild cart with fresh data
            logger.info("Step 2: Rebuilding cart with fresh pricing")
            rebuilt_cart = await rebuild_cart(original_cart, session)

            if not rebuilt_cart.get('success'):
                # If rebuild fails, return original data with warning
                logger.warning("Cart rebuild failed, returning original pricing data")
                return {
                    'success': True,
                    'warning': 'Could not rebuild cart for fresh pricing',
                    'original_cart': original_cart,
                    'rebuilt_cart': None,
                    'comparison': None,
                    'step_failed': 'rebuild'
                }

            # Step 3: Compare pricing
            logger.info("Step 3: Comparing original vs fresh pricing")
            comparison = await compare_pricing(original_cart, rebuilt_cart)

            return {
                'success': True,
                'group_link': group_link,
                'original_group_uuid': group_uuid,
                'new_group_uuid': rebuilt_cart.get('new_group_uuid'),
                'original_cart': original_cart,
                'rebuilt_cart': rebuilt_cart,
                'comparison': comparison,
                'timestamp': datetime.now().isoformat(),
                'extraction_method': original_cart.get('extraction_method'),
                'reconstruction_method': rebuilt_cart.get('reconstruction_method')
            }

        finally:
            await session.close()

    except Exception as e:
        logger.error(f"Error in comprehensive price check: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'group_link': group_link,
            'timestamp': datetime.now().isoformat()
        }


def format_pricing_summary(pricing_data: Dict[str, Any]) -> str:
    """
    Format pricing data into a human-readable summary.

    Args:
        pricing_data: Result from check_group_order_pricing()

    Returns:
        Formatted string summary of pricing information
    """
    try:
        if not pricing_data.get('success'):
            return f"❌ Price check failed: {pricing_data.get('error', 'Unknown error')}"

        # Build summary
        summary_lines = []
        summary_lines.append("🔍 **Price Check Summary**")
        summary_lines.append("")

        # Get original cart data
        original_cart = pricing_data.get('original_cart', {})
        original_pricing = original_cart.get('pricing_breakdown', {})
        currency_info = original_cart.get('currency_info', {})
        currency_symbol = currency_info.get('symbol', '$')

        # Show original pricing (always available)
        if original_pricing:
            original_total = original_pricing.get('total', 0.0)
            subtotal = original_pricing.get('subtotal', 0.0)

            summary_lines.append(f"� **Original Subtotal:** {currency_symbol}{subtotal:.2f}")
            summary_lines.append(f"💰 **Original Total:** {currency_symbol}{original_total:.2f}")

            # Show fee breakdown
            if original_pricing.get('delivery_fee', 0) > 0:
                summary_lines.append(f"🚚 **Delivery Fee:** {currency_symbol}{original_pricing.get('delivery_fee', 0):.2f}")
            if original_pricing.get('service_fee', 0) > 0:
                summary_lines.append(f"🔧 **Service Fee:** {currency_symbol}{original_pricing.get('service_fee', 0):.2f}")
            if original_pricing.get('taxes', 0) > 0:
                summary_lines.append(f"💸 **Taxes:** {currency_symbol}{original_pricing.get('taxes', 0):.2f}")

        # Check if we have comparison data
        comparison = pricing_data.get('comparison')
        if comparison and comparison.get('success'):
            rebuilt_total = comparison.get('rebuilt_pricing', {}).get('total', 0.0)
            difference = comparison.get('total_difference', 0.0)
            percentage = comparison.get('percentage_change', 0.0)

            summary_lines.append("")
            summary_lines.append(f"🔄 **Fresh Total:** {currency_symbol}{rebuilt_total:.2f}")

            if abs(difference) > 0.01:
                change_emoji = "📈" if difference > 0 else "📉"
                summary_lines.append(f"{change_emoji} **Price Change:** {currency_symbol}{difference:+.2f} ({percentage:+.1f}%)")
            else:
                summary_lines.append("✅ **Price Change:** No significant change")

            # Recommendations
            recommendations = comparison.get('recommendations', [])
            if recommendations:
                summary_lines.append("")
                summary_lines.append("💡 **Recommendations:**")
                for rec in recommendations:
                    summary_lines.append(f"• {rec}")

            # Item availability
            items_comparison = comparison.get('items_comparison', [])
            unavailable_items = [item for item in items_comparison if item.get('status') == 'unavailable']

            if unavailable_items:
                summary_lines.append("")
                summary_lines.append("⚠️ **Unavailable Items:**")
                for item in unavailable_items:
                    summary_lines.append(f"• {item.get('name', 'Unknown Item')}")
        else:
            # No comparison available
            summary_lines.append("")
            if pricing_data.get('warning'):
                summary_lines.append(f"⚠️ **Warning:** {pricing_data.get('warning')}")
            else:
                summary_lines.append("⚠️ **Note:** Fresh pricing comparison not available")

        # Show cart items
        cart_items = original_cart.get('cart_items', [])
        if cart_items:
            summary_lines.append("")
            summary_lines.append(f"🛒 **Cart Items ({len(cart_items)} items):**")
            for i, item in enumerate(cart_items[:5], 1):  # Show first 5 items
                name = item.get('name', 'Unknown Item')
                quantity = item.get('quantity', 1)
                price = item.get('price', 0)
                if name and name != 'Unknown Item':
                    summary_lines.append(f"  {i}. {name} (x{quantity}) - {currency_symbol}{price:.2f}")

            if len(cart_items) > 5:
                summary_lines.append(f"  ... and {len(cart_items) - 5} more items")

        # Show store info
        store_info = original_cart.get('store_info', {})
        if store_info.get('name'):
            summary_lines.append("")
            summary_lines.append(f"🏪 **Store:** {store_info.get('name', 'Unknown Store')}")

        return "\n".join(summary_lines)

    except Exception as e:
        logger.error(f"Error formatting pricing summary: {str(e)}")
        import traceback
        traceback.print_exc()
        return f"❌ Error formatting summary: {str(e)}"


# Example usage and testing functions
async def main():
    """
    Example usage of the PriceCheckerV2 system.
    """
    # Example group order link (replace with actual link for testing)
    example_link = "https://www.ubereats.com/orders/group-orders/12345678-1234-1234-1234-123456789012"

    print("🚀 Starting PriceCheckerV2 Example")
    print(f"📋 Checking pricing for: {example_link}")

    # Run comprehensive price check
    result = await check_group_order_pricing(example_link)

    # Display results
    if result.get('success'):
        print("✅ Price check completed successfully!")
 
        # Show formatted summary
        summary = format_pricing_summary(result)
        print("\n" + summary)

        # Show detailed data (optional)
        print("\n📊 **Detailed Results:**")
        print(f"Original extraction method: {result.get('extraction_method', 'Unknown')}")
        print(f"Reconstruction method: {result.get('reconstruction_method', 'Unknown')}")

        if result.get('new_group_uuid'):
            print(f"New group UUID: {result.get('new_group_uuid')}")

    else:
        print(f"❌ Price check failed: {result.get('error', 'Unknown error')}")
        if result.get('step_failed'):
            print(f"Failed at step: {result.get('step_failed')}")


async def set_tip_amount(draft_order_uuid: str, tip_amount: float, personal_cookies: Optional[str] = None) -> Dict[str, Any]:
    """
    Set tip amount for a draft order.

    Args:
        draft_order_uuid: Draft order UUID
        tip_amount: Tip amount in dollars (e.g., 0.0 for no tip)
        personal_cookies: Personal account cookies (optional)

    Returns:
        Dictionary with operation result
    """
    try:
        # Use provided cookies or global configuration
        if personal_cookies:
            auth_cookies = personal_cookies
        else:
            auth_cookies = PERSONAL_ACCOUNT_COOKIES

        if not auth_cookies or auth_cookies.strip() == "REPLACE_WITH_YOUR_PERSONAL_ACCOUNT_COOKIES":
            return {
                'success': False,
                'error': 'Personal account cookies not configured for tip setting'
            }

        # Parse cookies and create session
        cookies = parse_cookies(auth_cookies)

        async with aiohttp.ClientSession(cookies=cookies) as session:
            # Convert tip amount to E5 format (multiply by 100000)
            tip_amount_e5 = int(tip_amount * 100000)

            payload = {
                "draftOrderUUID": draft_order_uuid,
                "upfrontTipAmountE5": tip_amount_e5
            }

            logger.info(f"Setting tip to ${tip_amount:.2f} for draft order: {draft_order_uuid}")
            result = await make_api_request(session, "updateDraftOrderV2", payload, use_personal_headers=True)

            if result.get('success'):
                logger.info(f"Successfully set tip to ${tip_amount:.2f}")
                return {
                    'success': True,
                    'tip_amount': tip_amount,
                    'message': f'Tip set to ${tip_amount:.2f}'
                }
            else:
                logger.warning(f"Failed to set tip: {result.get('error', 'Unknown error')}")
                return {
                    'success': False,
                    'error': result.get('error', 'Unknown error')
                }

    except Exception as e:
        logger.error(f"Exception setting tip amount: {str(e)}")
        return {
            'success': False,
            'error': f'Set tip failed: {str(e)}'
        }


async def disable_promotions(draft_order_uuid: str, personal_cookies: Optional[str] = None) -> Dict[str, Any]:
    """
    Disable all promotions and discounts for a draft order to ensure consistent pricing.

    This function calls updateDraftOrderV2 API to remove any promotional discounts
    that might affect pricing accuracy during analysis.

    Args:
        draft_order_uuid: UUID of the draft order
        personal_cookies: Personal account cookies (optional)

    Returns:
        Dictionary with success status and any error messages
    """
    try:
        # Use provided cookies or global configuration
        if personal_cookies:
            auth_cookies = personal_cookies
        else:
            auth_cookies = PERSONAL_ACCOUNT_COOKIES

        if not auth_cookies or auth_cookies.strip() == "REPLACE_WITH_YOUR_PERSONAL_ACCOUNT_COOKIES":
            return {
                'success': False,
                'error': 'Personal account cookies not configured for promotion disabling'
            }

        # Parse cookies and create session
        cookies = parse_cookies(auth_cookies)

        async with aiohttp.ClientSession(cookies=cookies) as session:
            # Prepare the payload for disabling promotions
            payload = {
                "promotionOptions": {
                    "selectedPromotionInstanceUUIDs": [],
                    "skipApplyingPromotion": True
                },
                "draftOrderUUID": draft_order_uuid
            }

            logger.info(f"Disabling promotions for draft order: {draft_order_uuid}")
            result = await make_api_request(session, "updateDraftOrderV2", payload, use_personal_headers=True)

            if result.get('success'):
                logger.info(f"✅ Successfully disabled promotions for consistent pricing")
                return {
                    'success': True,
                    'promotions_disabled': True,
                    'message': 'Promotions disabled for consistent pricing'
                }
            else:
                logger.warning(f"⚠️ Failed to disable promotions: {result.get('error', 'Unknown error')}")
                return {
                    'success': False,
                    'error': result.get('error', 'Unknown error')
                }

    except Exception as e:
        logger.error(f"Exception disabling promotions: {str(e)}")
        return {
            'success': False,
            'error': f'Disable promotions failed: {str(e)}'
        }


async def cleanup_account_after_pricing(group_uuid: str, draft_order_uuid: str, store_uuid: str,
                                       personal_cookies: Optional[str] = None) -> Dict[str, Any]:
    """
    Clean up account by leaving group order and deleting personal cart.

    This maintains account hygiene by removing traces of the pricing test.

    Args:
        group_uuid: Group order UUID to leave
        draft_order_uuid: Personal cart UUID to delete
        store_uuid: Store UUID for cart deletion
        personal_cookies: Personal account cookies (optional)

    Returns:
        Dictionary with cleanup results
    """
    try:
        # Use provided cookies or global configuration
        if personal_cookies:
            auth_cookies = personal_cookies
        else:
            auth_cookies = PERSONAL_ACCOUNT_COOKIES

        if not auth_cookies or auth_cookies.strip() == "REPLACE_WITH_YOUR_PERSONAL_ACCOUNT_COOKIES":
            return {
                'success': False,
                'error': 'Personal account cookies not configured for cleanup',
                'left_group_order': False,
                'deleted_personal_cart': False
            }

        # Parse cookies and create session
        cookies = parse_cookies(auth_cookies)

        cleanup_results = {
            'left_group_order': False,
            'deleted_personal_cart': False
        }

        async with aiohttp.ClientSession(cookies=cookies) as session:
            # Step 1: Leave the group order
            leave_payload = {
                "draftOrderUuid": group_uuid
            }

            logger.info(f"Leaving group order: {group_uuid}")
            leave_result = await make_api_request(session, "removeMemberFromDraftOrderV1", leave_payload, use_personal_headers=True)

            if leave_result.get('success'):
                logger.info("Successfully left group order")
                cleanup_results['left_group_order'] = True
            else:
                logger.warning(f"Failed to leave group order: {leave_result.get('error', 'Unknown error')}")

            # Step 2: Delete the personal cart
            if draft_order_uuid and store_uuid:
                discard_payload = {
                    "draftOrderUUIDs": [draft_order_uuid],
                    "storeUUID": store_uuid
                }

                logger.info(f"Deleting personal cart: {draft_order_uuid}")
                discard_result = await make_api_request(session, "discardDraftOrdersV1", discard_payload, use_personal_headers=True)

                if discard_result.get('success'):
                    logger.info("Successfully deleted personal cart")
                    cleanup_results['deleted_personal_cart'] = True
                else:
                    logger.warning(f"Failed to delete personal cart: {discard_result.get('error', 'Unknown error')}")
            else:
                logger.warning("Missing draft order UUID or store UUID - cannot delete cart")

        # Determine overall success
        overall_success = cleanup_results['left_group_order'] and cleanup_results['deleted_personal_cart']

        return {
            'success': overall_success,
            'error': None if overall_success else 'Partial cleanup failure',
            'left_group_order': cleanup_results['left_group_order'],
            'deleted_personal_cart': cleanup_results['deleted_personal_cart'],
            'message': 'Account cleanup completed successfully' if overall_success else 'Partial cleanup completed'
        }

    except Exception as e:
        logger.error(f"Exception during account cleanup: {str(e)}")
        return {
            'success': False,
            'error': f'Cleanup failed: {str(e)}',
            'left_group_order': False,
            'deleted_personal_cart': False
        }


async def complete_price_analysis_with_cleanup_simple(group_link: str, personal_cookies: Optional[str] = None) -> Dict[str, Any]:
    """
    Complete 4-phase price analysis workflow with account cleanup - SIMPLE VERSION.

    This is the main function for integration into the method bot.
    Uses the proven workflow from comprehensive_test.py.

    Args:
        group_link: Group order URL
        personal_cookies: Personal account cookies (optional)

    Returns:
        Dictionary with complete analysis results
    """
    try:
        # Use provided cookies or global configuration
        if personal_cookies:
            auth_cookies = personal_cookies
        else:
            auth_cookies = PERSONAL_ACCOUNT_COOKIES

        if not auth_cookies or auth_cookies.strip() == "REPLACE_WITH_YOUR_PERSONAL_ACCOUNT_COOKIES":
            return {
                'success': False,
                'phase_failed': 'Authentication',
                'error': 'Personal account cookies not configured',
                'pricing_comparison': {},
                'cleanup_performed': False
            }

        # Extract group UUID from URL
        group_uuid = extract_group_uuid_from_link(group_link)

        # Parse cookies and create session
        cookies = parse_cookies(auth_cookies)

        async with aiohttp.ClientSession(cookies=cookies) as session:
            # Phase 1: Join group order and extract complete items
            logger.info("Phase 1: Joining group order and extracting items")

            # Join the group order first
            join_payload = {"draftOrderUuid": group_uuid}
            join_result = await make_api_request(session, "addMemberToDraftOrderV1", join_payload, use_personal_headers=True)

            if not join_result.get('success'):
                return {
                    'success': False,
                    'phase_failed': 'Phase 1: Join Group Order',
                    'error': f"Failed to join group order: {join_result.get('error', 'Unknown error')}",
                    'pricing_comparison': {},
                    'cleanup_performed': False
                }

            # Extract complete cart info (now as a member)
            cart_data = await extract_complete_cart_info(group_uuid, session)

            if not cart_data.get('success'):
                return {
                    'success': False,
                    'phase_failed': 'Phase 1: Extract Items',
                    'error': f"Failed to extract items: {cart_data.get('error', 'Unknown error')}",
                    'pricing_comparison': {},
                    'cleanup_performed': False
                }

            # Get extracted data
            cart_items = cart_data.get('cart_items', [])
            store_info = cart_data.get('store_info', {})
            store_uuid = store_info.get('uuid')
            original_total = cart_data.get('pricing_breakdown', {}).get('total', 0)

            if not cart_items or not store_uuid:
                return {
                    'success': False,
                    'phase_failed': 'Phase 1: Missing Data',
                    'error': 'No items or store UUID found',
                    'pricing_comparison': {},
                    'cleanup_performed': False
                }

            # Phase 2: Create personal cart using complete item structures
            logger.info("Phase 2: Creating personal cart with complete item structures")

            # Use the complete items directly (they already have all required fields)
            first_item = cart_items[0]

            # Create draft order with first item
            draft_payload = {
                "isMulticart": True,
                "storeUUID": store_uuid,
                "items": [first_item]  # Use complete item structure directly
            }

            draft_result = await make_api_request(session, "createDraftOrderV2", draft_payload, use_personal_headers=True)

            if not draft_result.get('success'):
                cleanup_result = await cleanup_account_after_pricing(group_uuid, None, store_uuid, auth_cookies)
                return {
                    'success': False,
                    'phase_failed': 'Phase 2: Create Cart',
                    'error': f"Failed to create cart: {draft_result.get('error', 'Unknown error')}",
                    'pricing_comparison': {},
                    'cleanup_performed': cleanup_result.get('success', False)
                }

            # Extract draft order UUID
            draft_response = draft_result.get('data', {})
            draft_order_uuid = draft_response.get('draftOrderUUID')

            if not draft_order_uuid:
                cleanup_result = await cleanup_account_after_pricing(group_uuid, None, store_uuid, auth_cookies)
                return {
                    'success': False,
                    'phase_failed': 'Phase 2: Missing Draft UUID',
                    'error': 'No draft order UUID returned',
                    'pricing_comparison': {},
                    'cleanup_performed': cleanup_result.get('success', False)
                }

            # Set tip to $0 for consistent pricing
            tip_result = await set_tip_amount(draft_order_uuid, 0.0, auth_cookies)
            if not tip_result.get('success'):
                logger.warning(f"Failed to set tip: {tip_result.get('error')}")

            # Phase 2.6: Disable promotions for consistent pricing
            logger.info("Phase 2.6: Disabling promotions for consistent pricing")
            promotion_result = await disable_promotions(draft_order_uuid, auth_cookies)

            if not promotion_result.get('success'):
                logger.warning(f"Failed to disable promotions: {promotion_result.get('error')}")
                logger.warning("Pricing may include promotional discounts")

            # Phase 3: Get real-time pricing
            logger.info("Phase 3: Getting real-time pricing")
            pricing_result = await simulate_checkout(draft_order_uuid, personal_cookies=auth_cookies)

            if not pricing_result.get('success'):
                cleanup_result = await cleanup_account_after_pricing(group_uuid, draft_order_uuid, store_uuid, auth_cookies)
                return {
                    'success': False,
                    'phase_failed': 'Phase 3: Get Pricing',
                    'error': f"Failed to get pricing: {pricing_result.get('error', 'Unknown error')}",
                    'pricing_comparison': {},
                    'cleanup_performed': cleanup_result.get('success', False)
                }

            # Extract pricing information
            pricing_breakdown = pricing_result.get('pricing_breakdown', {})
            current_total = pricing_breakdown.get('total', 0)

            # Calculate price comparison
            price_difference = current_total - original_total
            percentage_change = (price_difference / original_total * 100) if original_total > 0 else 0

            # Phase 4: Clean up account (configurable via debug toggle)
            if ENABLE_PHASE4_CLEANUP:
                logger.info("Phase 4: Cleaning up account after pricing analysis")
                cleanup_result = await cleanup_account_after_pricing(group_uuid, draft_order_uuid, store_uuid, auth_cookies)

                if cleanup_result.get('success'):
                    logger.info("✅ Phase 4 completed successfully - Account cleaned up")
                else:
                    logger.warning(f"⚠️ Phase 4 partial failure: {cleanup_result.get('error', 'Unknown cleanup error')}")
            else:
                logger.info("🔍 Phase 4: Cleanup DISABLED for debugging - Cart preserved for inspection")
                if DEBUG_PHASE4_LOGGING:
                    logger.info(f"🔍 DEBUG: Draft Order UUID: {draft_order_uuid}")
                    logger.info(f"🔍 DEBUG: Group Order UUID: {group_uuid}")
                    logger.info(f"🔍 DEBUG: Cart URL: https://www.ubereats.com/checkout?draftOrderUuid={draft_order_uuid}")

                # Mock cleanup result for consistent response structure
                cleanup_result = {
                    'success': False,
                    'debug_mode_enabled': True,
                    'left_group_order': False,
                    'deleted_personal_cart': False,
                    'message': 'Cleanup disabled for debugging - cart preserved for inspection'
                }

            # Return success results with debug information
            return {
                'success': True,
                'phase_failed': None,
                'error': None,
                'pricing_comparison': {
                    'original_total': original_total,
                    'current_total': current_total,
                    'price_difference': price_difference,
                    'percentage_change': percentage_change,
                    'pricing_breakdown': pricing_breakdown,
                    'store_info': store_info,
                    'items_count': len(cart_items),
                    'estimation_used': pricing_result.get('estimation_used', False),
                    'raw_api_total': pricing_result.get('raw_api_total')
                },
                'cleanup_performed': cleanup_result.get('success', False),
                'cleanup_details': {
                    'left_group_order': cleanup_result.get('left_group_order', False),
                    'deleted_personal_cart': cleanup_result.get('deleted_personal_cart', False),
                    'cleanup_message': cleanup_result.get('message', 'Cleanup status unknown')
                },
                'message': f"Price analysis complete: ${original_total:.2f} → ${current_total:.2f} ({percentage_change:+.1f}%)",
                'raw_response': pricing_result.get('raw_response', {})
            }

    except Exception as e:
        logger.error(f"Exception in complete price analysis: {str(e)}")
        return {
            'success': False,
            'phase_failed': 'Exception',
            'error': f'Analysis failed: {str(e)}',
            'pricing_comparison': {},
            'cleanup_performed': False
        }


async def complete_price_analysis_with_cleanup(group_link: str, personal_cookies: Optional[str] = None) -> Dict[str, Any]:
    """
    Complete 4-phase price analysis workflow with account cleanup.

    This is the main function for integration into the method bot.

    Phases:
    1. Join group order and extract items
    2. Create personal cart with items
    3. Get real-time pricing comparison
    4. Clean up account (leave group order, delete cart)

    Args:
        group_link: Group order URL
        personal_cookies: Personal account cookies (optional)

    Returns:
        Dictionary with complete analysis results
    """
    try:
        # Phase 1: Extract group order items
        logger.info("Phase 1: Starting group order extraction")
        extraction_result = await add_group_order_to_personal_cart(group_link, personal_cookies)

        if not extraction_result.get('success'):
            return {
                'success': False,
                'phase_failed': 'Phase 1: Group Order Extraction',
                'error': extraction_result.get('error', 'Unknown error'),
                'pricing_comparison': {},
                'cleanup_performed': False
            }

        # Extract key information for subsequent phases
        draft_order_uuid = extraction_result.get('draft_order_uuid')
        store_info = extraction_result.get('store_info', {})
        store_uuid = store_info.get('uuid')
        group_uuid = extract_group_uuid_from_link(group_link)
        original_total = extraction_result.get('original_total', 0)

        if not draft_order_uuid or not store_uuid:
            return {
                'success': False,
                'phase_failed': 'Phase 1: Missing required data',
                'error': 'Could not extract draft order UUID or store UUID',
                'pricing_comparison': {},
                'cleanup_performed': False
            }

        # Phase 2: Set tip to $0 for consistent pricing
        logger.info("Phase 2: Setting tip to $0.00 for consistent pricing")
        tip_result = await set_tip_amount(draft_order_uuid, 0.0, personal_cookies)

        if not tip_result.get('success'):
            logger.warning(f"Failed to set tip: {tip_result.get('error')}")

        # Phase 2.6: Disable promotions for consistent pricing
        logger.info("Phase 2.6: Disabling promotions for consistent pricing")
        promotion_result = await disable_promotions(draft_order_uuid, personal_cookies)

        if not promotion_result.get('success'):
            logger.warning(f"Failed to disable promotions: {promotion_result.get('error')}")
            logger.warning("Pricing may include promotional discounts")

        # Phase 3: Get real-time pricing
        logger.info("Phase 3: Getting real-time pricing")
        pricing_result = await simulate_checkout(draft_order_uuid, personal_cookies)

        if not pricing_result.get('success'):
            # Cleanup account even if pricing fails (respects debug toggle)
            if ENABLE_PHASE4_CLEANUP:
                logger.info("Phase 4: Cleaning up account after pricing failure")
                cleanup_result = await cleanup_account_after_pricing(group_uuid, draft_order_uuid, store_uuid, personal_cookies)
            else:
                logger.info("🔍 Phase 4: Cleanup DISABLED for debugging - Cart preserved after pricing failure")
                cleanup_result = {
                    'success': False,
                    'debug_mode_enabled': True,
                    'left_group_order': False,
                    'deleted_personal_cart': False,
                    'message': 'Cleanup disabled for debugging - cart preserved after pricing failure'
                }

            return {
                'success': False,
                'phase_failed': 'Phase 3: Real-time Pricing',
                'error': pricing_result.get('error', 'Unknown error'),
                'pricing_comparison': {},
                'cleanup_performed': cleanup_result.get('success', False)
            }

        # Extract pricing information
        pricing_breakdown = pricing_result.get('pricing_breakdown', {})
        current_total = pricing_breakdown.get('total', 0)

        # Calculate price comparison
        price_difference = current_total - original_total
        percentage_change = (price_difference / original_total * 100) if original_total > 0 else 0

        # Phase 4: Clean up account (configurable via debug toggle)
        if ENABLE_PHASE4_CLEANUP:
            logger.info("Phase 4: Cleaning up account after pricing analysis")
            cleanup_result = await cleanup_account_after_pricing(group_uuid, draft_order_uuid, store_uuid, personal_cookies)

            if cleanup_result.get('success'):
                logger.info("✅ Phase 4 completed successfully - Account cleaned up")
            else:
                logger.warning(f"⚠️ Phase 4 partial failure: {cleanup_result.get('error', 'Unknown cleanup error')}")
        else:
            logger.info("🔍 Phase 4: Cleanup DISABLED for debugging - Cart preserved for inspection")
            if DEBUG_PHASE4_LOGGING:
                logger.info(f"🔍 DEBUG: Draft Order UUID: {draft_order_uuid}")
                logger.info(f"🔍 DEBUG: Group Order UUID: {group_uuid}")
                logger.info(f"🔍 DEBUG: Cart URL: https://www.ubereats.com/checkout?draftOrderUuid={draft_order_uuid}")

            # Mock cleanup result for consistent response structure
            cleanup_result = {
                'success': False,
                'debug_mode_enabled': True,
                'left_group_order': False,
                'deleted_personal_cart': False,
                'message': 'Cleanup disabled for debugging - cart preserved for inspection'
            }

        # Prepare final results with debug information
        return {
            'success': True,
            'phase_failed': None,
            'error': None,
            'pricing_comparison': {
                'original_total': original_total,
                'current_total': current_total,
                'price_difference': price_difference,
                'percentage_change': percentage_change,
                'pricing_breakdown': pricing_breakdown,
                'store_info': store_info,
                'items_count': len(extraction_result.get('items_processed', [])),
                'estimation_used': pricing_result.get('estimation_used', False),
                'raw_api_total': pricing_result.get('raw_api_total')
            },
            'cleanup_performed': cleanup_result.get('success', False),
            'cleanup_details': {
                'left_group_order': cleanup_result.get('left_group_order', False),
                'deleted_personal_cart': cleanup_result.get('deleted_personal_cart', False),
                'cleanup_message': cleanup_result.get('message', 'Cleanup status unknown')
            },
            'message': f"Price analysis complete: ${original_total:.2f} → ${current_total:.2f} ({percentage_change:+.1f}%)",
            'raw_response': pricing_result.get('raw_response', {})
        }

    except Exception as e:
        logger.error(f"Exception in complete price analysis: {str(e)}")
        return {
            'success': False,
            'phase_failed': 'Exception',
            'error': f'Analysis failed: {str(e)}',
            'pricing_comparison': {},
            'cleanup_performed': False
        }


async def _create_cart_with_complete_items(session: aiohttp.ClientSession, cart_items: List[Dict], store_uuid: str, delivery_address: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Create personal cart using complete item structures from getDraftOrderByUuidV2.

    This is the breakthrough method that uses complete item data directly
    instead of fetching fresh data which often fails.

    Args:
        session: Authenticated aiohttp session
        cart_items: Complete item structures from group order
        store_uuid: Store UUID

    Returns:
        Dictionary with cart creation results
    """
    try:
        if not cart_items:
            return {
                'success': False,
                'error': 'No items provided for cart creation'
            }

        # Prepare shopping cart items using complete item structures
        shopping_cart_items = []

        for item in cart_items:
            # Use complete item structure directly but add shopping cart item UUID
            import uuid
            item['shoppingCartItemUuid'] = str(uuid.uuid4())
            shopping_cart_items.append(item)

        # Create draft order with the correct payload structure (matching comprehensive_test.py)
        draft_payload = {
            "isMulticart": True,
            "shoppingCartItems": shopping_cart_items,
            "useCredits": True,
            "extraPaymentProfiles": [],
            "promotionOptions": {
                "autoApplyPromotionUUIDs": [],
                "selectedPromotionInstanceUUIDs": [],
                "skipApplyingPromotion": False
            },
            "deliveryTime": {"asap": True},
            "deliveryType": "ASAP",
            "currencyCode": "USD",
            "interactionType": "door_to_door",
            "paymentProfileUUID": "a1ca6530-e5e3-51e7-b44e-ab0605972fa3",
            "checkMultipleDraftOrdersCap": True,
            "actionMeta": {"isQuickAdd": False, "numClicks": 1},
            "businessDetails": {}
        }

        # CRITICAL FIX: Add delivery address directly to the payload if provided
        if delivery_address and isinstance(delivery_address, dict):
            # Use the raw delivery address structure from group order extraction
            raw_delivery_address = delivery_address.get('raw_delivery_address', {})
            if raw_delivery_address:
                draft_payload["deliveryAddress"] = raw_delivery_address
                logger.info(f"✅ Added delivery address to createDraftOrderV2 payload: {delivery_address.get('address1', '')}, {delivery_address.get('city', '')}, {delivery_address.get('state', '')}")
            else:
                # Fallback: construct delivery address from parsed fields
                draft_payload["deliveryAddress"] = {
                    "latitude": delivery_address.get('latitude', 0.0),
                    "longitude": delivery_address.get('longitude', 0.0),
                    "address": {
                        "address1": delivery_address.get('address1', ''),
                        "address2": delivery_address.get('address2', ''),
                        "aptOrSuite": "",
                        "eaterFormattedAddress": delivery_address.get('subtitle', ''),
                        "title": delivery_address.get('formatted_address', delivery_address.get('address1', '')),
                        "subtitle": delivery_address.get('subtitle', ''),
                        "uuid": ""
                    },
                    "reference": delivery_address.get('reference', ''),
                    "referenceType": delivery_address.get('reference_type', 'uber_places'),
                    "type": delivery_address.get('reference_type', 'uber_places'),
                    "addressComponents": {
                        "city": delivery_address.get('city', ''),
                        "countryCode": delivery_address.get('country', 'US'),
                        "firstLevelSubdivisionCode": delivery_address.get('state', ''),
                        "postalCode": delivery_address.get('zipcode', '')
                    }
                }
                logger.info(f"✅ Constructed delivery address for createDraftOrderV2 payload: {delivery_address.get('address1', '')}, {delivery_address.get('city', '')}, {delivery_address.get('state', '')}")
        else:
            logger.warning("⚠️  No delivery address provided to createDraftOrderV2 - may use account default")

        # CRITICAL FIX: Clear cached location cookie before cart creation
        # The uev2.loc cookie contains cached address data that overrides our changes

        logger.info("🔧 STEP 0: Clearing cached location cookie")

        if delivery_address and isinstance(delivery_address, dict):
            # Build the correct location data for the cookie
            new_location_data = {
                "latitude": delivery_address.get('latitude', 0.0),
                "longitude": delivery_address.get('longitude', 0.0),
                "address": {
                    "address1": delivery_address.get('address1', ''),
                    "address2": delivery_address.get('address2', ''),
                    "aptOrSuite": "",
                    "eaterFormattedAddress": delivery_address.get('subtitle', ''),
                    "subtitle": delivery_address.get('subtitle', ''),
                    "title": delivery_address.get('address1', ''),
                    "uuid": ""
                },
                "reference": delivery_address.get('reference', ''),
                "referenceType": delivery_address.get('reference_type', 'here_places'),
                "type": delivery_address.get('reference_type', 'here_places'),
                "addressComponents": {
                    "city": delivery_address.get('city', ''),
                    "countryCode": delivery_address.get('country', 'US'),
                    "firstLevelSubdivisionCode": delivery_address.get('state', ''),
                    "postalCode": delivery_address.get('zipcode', '')
                }
            }

            # URL encode the location data for the cookie
            import urllib.parse
            import json
            location_json = json.dumps(new_location_data)
            encoded_location = urllib.parse.quote(location_json)

            # Update the session cookie
            session.cookie_jar.update_cookies({'uev2.loc': encoded_location})

            logger.info(f"✅ Updated location cookie with Dallas address")
            logger.info(f"   New address: {delivery_address.get('address1', '')}, {delivery_address.get('city', '')}, {delivery_address.get('state', '')}")
        else:
            logger.warning("⚠️  No delivery address to set in cookie")

        logger.info(f"🔧 STEP 1: Creating personal cart with {len(shopping_cart_items)} items")
        logger.info("   Note: Cart should now use correct address from updated cookie")
        draft_result = await make_api_request(session, "createDraftOrderV2", draft_payload, use_personal_headers=True)

        if not draft_result.get('success'):
            return {
                'success': False,
                'error': f"Failed to create draft order: {draft_result.get('error', 'Unknown error')}"
            }

        # Extract draft order UUID from response (matching comprehensive_test.py structure)
        if not (draft_result.get('success') and draft_result.get('data', {}).get('status') == 'success'):
            return {
                'success': False,
                'error': f"createDraftOrderV2 failed: {draft_result.get('error', 'Unknown error')}"
            }

        response_data = draft_result.get('data', {})
        data_section = response_data.get('data', {})
        draft_order = data_section.get('draftOrder', {})
        draft_order_uuid = draft_order.get('uuid')

        if not draft_order_uuid:
            return {
                'success': False,
                'error': 'No draft order UUID found in createDraftOrderV2 response'
            }

        logger.info(f"Successfully created draft order with {len(cart_items)} items: {draft_order_uuid}")

        return {
            'success': True,
            'draft_order_uuid': draft_order_uuid,
            'items_processed': len(cart_items),
            'message': f'Successfully created cart with {len(cart_items)} items'
        }

    except Exception as e:
        logger.error(f"Exception in _create_cart_with_complete_items: {str(e)}")
        return {
            'success': False,
            'error': f'Cart creation failed: {str(e)}'
        }


# MAIN INTEGRATION FUNCTION FOR QUICKBITES BOT
async def quickbites_bot_price_analysis(group_link: str, personal_cookies: Optional[str] = None) -> Dict[str, Any]:
    """
    PRODUCTION-READY function for quickbites bot integration.

    Complete 4-phase price analysis with account cleanup.
    Uses the proven workflow from comprehensive testing.

    Args:
        group_link: Group order URL
        personal_cookies: Personal account cookies (optional)

    Returns:
        Dictionary with complete analysis results
    """
    try:
        # Use provided cookies or global configuration
        if personal_cookies:
            auth_cookies = personal_cookies
        else:
            auth_cookies = PERSONAL_ACCOUNT_COOKIES

        if not auth_cookies or auth_cookies.strip() == "REPLACE_WITH_YOUR_PERSONAL_ACCOUNT_COOKIES":
            return {
                'success': False,
                'error': 'Personal account cookies not configured',
                'pricing_comparison': {},
                'cleanup_performed': False
            }

        # Extract group UUID
        group_uuid = extract_group_uuid_from_link(group_link)

        # Parse cookies and create session
        cookies = parse_cookies(auth_cookies)

        async with aiohttp.ClientSession(cookies=cookies) as session:
            # Phase 1: Join group order and extract items
            logger.info("Phase 1: Joining group order and extracting items")

            # Join group order first (critical step)
            join_payload = {"draftOrderUuid": group_uuid}
            join_result = await make_api_request(session, "addMemberToDraftOrderV1", join_payload, use_personal_headers=True)

            if not join_result.get('success'):
                return {
                    'success': False,
                    'error': f"Failed to join group order: {join_result.get('error', 'Unknown error')}",
                    'pricing_comparison': {},
                    'cleanup_performed': False
                }

            # Extract complete cart info
            cart_data = await extract_complete_cart_info(group_uuid, session)

            if not cart_data.get('success'):
                return {
                    'success': False,
                    'error': f"Failed to extract items: {cart_data.get('error', 'Unknown error')}",
                    'pricing_comparison': {},
                    'cleanup_performed': False
                }

            # Get data
            cart_items = cart_data.get('cart_items', [])
            store_info = cart_data.get('store_info', {})
            store_uuid = store_info.get('uuid')
            original_total = cart_data.get('pricing_breakdown', {}).get('total', 0)

            # Phase 2: Create personal cart using complete item structures
            logger.info("Phase 2: Creating personal cart with complete item structures")

            # Use complete items directly (they already have all required fields from getDraftOrderByUuidV2)
            # CRITICAL FIX: Pass delivery address to ensure consistent pricing
            delivery_address = cart_data.get('delivery_address', {})
            add_result = await _create_cart_with_complete_items(session, cart_items, store_uuid, delivery_address)

            if not add_result.get('success'):
                cleanup_result = await cleanup_account_after_pricing(group_uuid, None, store_uuid, auth_cookies)
                return {
                    'success': False,
                    'error': f"Failed to create personal cart: {add_result.get('error', 'Unknown error')}",
                    'pricing_comparison': {},
                    'cleanup_performed': cleanup_result.get('success', False)
                }

            draft_order_uuid = add_result.get('draft_order_uuid')

            # CRITICAL FIX: Link address change to the specific draft order (MISSING PIECE!)
            logger.info("🔧 STEP 2.5: Linking delivery address to draft order")

            if delivery_address and isinstance(delivery_address, dict):
                logger.info(f"   Setting address: {delivery_address.get('address1', 'Unknown')}, {delivery_address.get('city', 'Unknown')}, {delivery_address.get('state', 'Unknown')}")
                logger.info(f"   For draft order: {draft_order_uuid}")

                # CRITICAL: Link the address change to the specific draft order
                address_result = await _link_address_to_draft_order(session, delivery_address, draft_order_uuid)

                if address_result.get('success'):
                    logger.info("✅ Successfully linked address to draft order")

                    # Wait for the address change to propagate
                    await asyncio.sleep(2)

                    logger.info("✅ Address linked to cart - pricing should now use correct location")
                else:
                    logger.warning(f"⚠️  Failed to link address to draft order: {address_result.get('error')}")
                    logger.warning("   Pricing may use incorrect delivery address")
            else:
                logger.warning("⚠️  No delivery address to link - using account default")

            # Phase 2.5: Set tip to $0
            logger.info("Phase 2.5: Setting tip to $0.00")
            tip_result = await set_tip_amount(draft_order_uuid, 0.0, auth_cookies)

            # Phase 2.6: Disable promotions for consistent pricing
            logger.info("Phase 2.6: Disabling promotions for consistent pricing")
            promotion_result = await disable_promotions(draft_order_uuid, auth_cookies)

            if not promotion_result.get('success'):
                logger.warning(f"Failed to disable promotions: {promotion_result.get('error')}")
                logger.warning("Pricing may include promotional discounts")

            # Phase 3: Get real-time pricing
            logger.info("Phase 3: Getting real-time pricing")
            pricing_result = await simulate_checkout(draft_order_uuid, personal_cookies=auth_cookies)

            if not pricing_result.get('success'):
                # Cleanup account even if pricing fails (respects debug toggle)
                if ENABLE_PHASE4_CLEANUP:
                    cleanup_result = await cleanup_account_after_pricing(group_uuid, draft_order_uuid, store_uuid, auth_cookies)
                else:
                    logger.info("🔍 Phase 4: Cleanup DISABLED for debugging - Cart preserved after pricing failure")
                    cleanup_result = {
                        'success': False,
                        'debug_mode_enabled': True,
                        'left_group_order': False,
                        'deleted_personal_cart': False,
                        'message': 'Cleanup disabled for debugging - cart preserved after pricing failure'
                    }

                return {
                    'success': False,
                    'error': f"Failed to get pricing: {pricing_result.get('error', 'Unknown error')}",
                    'pricing_comparison': {},
                    'cleanup_performed': cleanup_result.get('success', False)
                }

            # Extract pricing
            pricing_breakdown = pricing_result.get('pricing_breakdown', {})
            current_total = pricing_breakdown.get('total', 0)
            price_difference = current_total - original_total
            percentage_change = (price_difference / original_total * 100) if original_total > 0 else 0

            # Phase 4: Clean up account (configurable via debug toggle)
            if ENABLE_PHASE4_CLEANUP:
                logger.info("Phase 4: Cleaning up account after pricing analysis")
                cleanup_result = await cleanup_account_after_pricing(group_uuid, draft_order_uuid, store_uuid, auth_cookies)

                if cleanup_result.get('success'):
                    logger.info("✅ Phase 4 completed successfully - Account cleaned up")
                else:
                    logger.warning(f"⚠️ Phase 4 partial failure: {cleanup_result.get('error', 'Unknown cleanup error')}")
            else:
                logger.info("🔍 Phase 4: Cleanup DISABLED for debugging - Cart preserved for inspection")
                if DEBUG_PHASE4_LOGGING:
                    logger.info(f"🔍 DEBUG: Draft Order UUID: {draft_order_uuid}")
                    logger.info(f"🔍 DEBUG: Group Order UUID: {group_uuid}")
                    logger.info(f"🔍 DEBUG: Cart URL: https://www.ubereats.com/checkout?draftOrderUuid={draft_order_uuid}")
                    logger.info(f"🔍 DEBUG: Group Order URL: https://eats.uber.com/group-orders/{group_uuid}/join")

                # Mock cleanup result for consistent response structure
                cleanup_result = {
                    'success': False,
                    'debug_mode_enabled': True,
                    'left_group_order': False,
                    'deleted_personal_cart': False,
                    'message': 'Cleanup disabled for debugging - cart preserved for inspection'
                }

            # Return results with debug information
            return {
                'success': True,
                'error': None,
                'pricing_comparison': {
                    'original_total': original_total,
                    'current_total': current_total,
                    'price_difference': price_difference,
                    'percentage_change': percentage_change,
                    'pricing_breakdown': pricing_breakdown,
                    'store_info': store_info,
                    'items_count': len(cart_items),
                    'estimation_used': pricing_result.get('estimation_used', False),
                    'raw_api_total': pricing_result.get('raw_api_total')
                },
                'cleanup_performed': cleanup_result.get('success', False),
                'cleanup_details': {
                    'left_group_order': cleanup_result.get('left_group_order', False),
                    'deleted_personal_cart': cleanup_result.get('deleted_personal_cart', False),
                    'cleanup_message': cleanup_result.get('message', 'Cleanup status unknown')
                },
                'message': f"${original_total:.2f} → ${current_total:.2f} ({percentage_change:+.1f}%)" + (" [DEBUG MODE - Cleanup Disabled]" if not ENABLE_PHASE4_CLEANUP else ""),
                'debug_info': {
                    'phase4_cleanup_enabled': ENABLE_PHASE4_CLEANUP,
                    'draft_order_uuid': draft_order_uuid,
                    'group_order_uuid': group_uuid,
                    'cart_url': f"https://www.ubereats.com/checkout?draftOrderUuid={draft_order_uuid}" if not ENABLE_PHASE4_CLEANUP else None,
                    'group_order_url': f"https://eats.uber.com/group-orders/{group_uuid}/join" if not ENABLE_PHASE4_CLEANUP else None
                } if not ENABLE_PHASE4_CLEANUP else None,
                'raw_response': pricing_result.get('raw_response', {})
            }

    except Exception as e:
        logger.error(f"Exception in method bot price analysis: {str(e)}")
        return {
            'success': False,
            'error': f'Analysis failed: {str(e)}',
            'pricing_comparison': {},
            'cleanup_performed': False
        }


async def _set_target_location_from_address(session: aiohttp.ClientSession, delivery_address: Dict[str, Any]) -> Dict[str, Any]:
    """
    Set the target delivery location in the user's account using the proper Uber Eats flow.

    This implements the complete address change flow that Uber Eats uses:
    1. Search for the address using mapsSearchV1
    2. Get delivery location details with getDeliveryLocationV2
    3. Get address form data with getAddressEntryFormV2
    4. Upsert the delivery location with upsertDeliveryLocationV2
    5. Set as target location with setTargetLocationV1

    Args:
        session: Authenticated aiohttp session
        delivery_address: Address dictionary from group order

    Returns:
        Dictionary with success status and any error messages
    """
    try:
        # Build the search query from address components
        address_parts = []
        if delivery_address.get('address1'):
            address_parts.append(delivery_address['address1'])
        if delivery_address.get('city'):
            address_parts.append(delivery_address['city'])
        if delivery_address.get('state'):
            address_parts.append(delivery_address['state'])

        if not address_parts:
            return {'success': False, 'error': 'No address components to search with'}

        search_query = ', '.join(address_parts)
        logger.info(f"🔍 Searching for address: {search_query}")

        # Step 1: Search for the address using mapsSearchV1
        search_payload = {
            "query": search_query
        }

        search_result = await make_api_request(session, "mapsSearchV1", search_payload, use_personal_headers=True)

        if not search_result.get('success'):
            return {'success': False, 'error': f'Address search failed: {search_result.get("error")}'}

        # Extract place information from search results
        # The response structure is: {"data": {"status": "success", "data": [{"id": "...", "provider": "here_places", ...}]}}
        search_response = search_result.get('data', {})
        logger.debug(f"Search response type: {type(search_response)}")

        # Extract the actual data array from the nested structure
        if isinstance(search_response, dict) and 'data' in search_response:
            search_data = search_response['data']
        else:
            search_data = search_response

        # The data should be a list of address results
        if not isinstance(search_data, list) or len(search_data) == 0:
            logger.error(f"No address results found in search response: {search_response}")
            return {'success': False, 'error': 'No address results found'}

        # Use the first result (most relevant)
        first_result = search_data[0]

        if not isinstance(first_result, dict):
            logger.error(f"Unexpected result format: {first_result}")
            return {'success': False, 'error': 'Unexpected result format'}

        # Extract place ID and provider from the result
        place_id = first_result.get('id', '')  # The field is 'id', not 'placeId'
        provider = first_result.get('provider', 'here_places')

        logger.debug(f"Extracted place_id: {place_id}, provider: {provider}")

        if not place_id:
            return {'success': False, 'error': 'No place ID found in search results'}

        logger.info(f"📍 Found place ID: {place_id}")

        # Step 2: Get delivery location details
        location_payload = {
            "placeId": place_id,
            "provider": provider,
            "source": "manual_auto_complete"
        }

        location_result = await make_api_request(session, "getDeliveryLocationV2", location_payload, use_personal_headers=True)

        if not location_result.get('success'):
            return {'success': False, 'error': f'Get delivery location failed: {location_result.get("error")}'}

        # Step 3: Get address entry form (required for the flow)
        form_payload = {
            "referenceInfo": {
                "placeID": place_id,
                "provider": provider
            }
        }

        form_result = await make_api_request(session, "getAddressEntryFormV2", form_payload, use_personal_headers=True)

        if not form_result.get('success'):
            logger.warning(f"Address form request failed: {form_result.get('error')} - continuing anyway")

        # Step 4: Upsert the delivery location
        upsert_payload = {
            "isTargetLocation": True,
            "analytics": [{
                "dataSourceType": "PLACE_DETAILS",
                "dataSourceEndpoint": "",
                "dataSourceImpressionID": "",
                "cached": False
            }],
            "deliveryInstruction": {
                "interactionType": "door_to_door"
            },
            "deliveryPayloadType": "USER_INPUT",
            "selectedInteractionType": "door_to_door",
            "referenceInfo": {
                "placeID": place_id,
                "provider": provider
            },
            "addressInfo": {
                "RESIDENCE_TYPE": "OTHER"
            },
            "label": ""
        }

        upsert_result = await make_api_request(session, "upsertDeliveryLocationV2", upsert_payload, use_personal_headers=True)

        if not upsert_result.get('success'):
            return {'success': False, 'error': f'Upsert delivery location failed: {upsert_result.get("error")}'}

        # Step 5: Get instruction for location (CRITICAL - missing step!)
        location_instruction_payload = {
            "location": {
                "address": {
                    "address1": delivery_address.get('address1', ''),
                    "address2": delivery_address.get('subtitle', ''),
                    "aptOrSuite": "",
                    "eaterFormattedAddress": delivery_address.get('subtitle', ''),
                    "subtitle": delivery_address.get('subtitle', ''),
                    "title": delivery_address.get('address1', ''),
                    "uuid": ""
                },
                "latitude": delivery_address.get('latitude', 0.0),
                "longitude": delivery_address.get('longitude', 0.0),
                "reference": place_id,
                "referenceType": provider,
                "type": provider,
                "addressComponents": {
                    "city": delivery_address.get('city', ''),
                    "countryCode": delivery_address.get('country', 'US'),
                    "firstLevelSubdivisionCode": delivery_address.get('state', ''),
                    "postalCode": delivery_address.get('zipcode', '')
                },
                "categories": ["place"],
                "originType": "user_autocomplete"
            }
        }

        instruction_result = await make_api_request(session, "getInstructionForLocationV1", location_instruction_payload, use_personal_headers=True)

        if not instruction_result.get('success'):
            logger.warning(f"⚠️  Get instruction for location failed: {instruction_result.get('error')} - continuing anyway")
        else:
            logger.info("✅ Successfully got instruction for location")

        # Step 6: Set as target location
        target_payload = {}  # Empty payload as per your XHR flow

        target_result = await make_api_request(session, "setTargetLocationV1", target_payload, use_personal_headers=True)

        if not target_result.get('success'):
            return {'success': False, 'error': f'Set target location failed: {target_result.get("error")}'}

        logger.info("✅ Successfully set target location")

        # Step 7: Refresh user state (CRITICAL - missing step!)
        user_result = await make_api_request(session, "getUserV1", {}, use_personal_headers=True)

        if not user_result.get('success'):
            logger.warning(f"⚠️  Get user failed: {user_result.get('error')} - continuing anyway")
        else:
            logger.info("✅ Successfully refreshed user state")

        logger.info(f"✅ Successfully completed full address change sequence: {search_query}")

        return {
            'success': True,
            'place_id': place_id,
            'provider': provider,
            'search_query': search_query
        }

    except Exception as e:
        logger.error(f"Error setting target location: {str(e)}")
        return {'success': False, 'error': str(e)}


async def _verify_target_location_set(session: aiohttp.ClientSession, expected_address: str) -> Dict[str, Any]:
    """
    Verify that the target location was actually set by checking the current delivery location.

    Args:
        session: Authenticated aiohttp session
        expected_address: The address we expect to be set

    Returns:
        Dictionary with success status and verification details
    """
    try:
        # Try to get the current delivery locations to verify the change took effect
        locations_payload = {
            "locationTypes": ["SUGGESTED"]
        }

        locations_result = await make_api_request(session, "getDeliveryLocationsV2", locations_payload, use_personal_headers=True)

        if not locations_result.get('success'):
            return {'success': False, 'error': f'Failed to get delivery locations: {locations_result.get("error")}'}

        # Check if any of the returned locations match our expected address
        locations_data = locations_result.get('data', {})

        if isinstance(locations_data, dict) and 'data' in locations_data:
            locations_list = locations_data['data']
        else:
            locations_list = locations_data

        if not isinstance(locations_list, list):
            return {'success': False, 'error': 'Unexpected locations response format'}

        # Look for our address in the locations
        expected_lower = expected_address.lower()

        for location in locations_list:
            if isinstance(location, dict):
                # Check various address fields
                address_fields = [
                    location.get('address', {}).get('address1', ''),
                    location.get('address', {}).get('title', ''),
                    location.get('formattedAddress', ''),
                    str(location.get('address', {}))
                ]

                for field in address_fields:
                    if field and expected_lower in field.lower():
                        logger.info(f"✅ Found matching location: {field}")
                        return {'success': True, 'matched_location': field}

        # If we get here, we didn't find a matching location
        logger.warning(f"⚠️  Expected address '{expected_address}' not found in current locations")
        return {'success': False, 'error': 'Target location not found in current locations'}

    except Exception as e:
        logger.error(f"Error verifying target location: {str(e)}")
        return {'success': False, 'error': str(e)}


async def _force_delivery_address_in_session(session: aiohttp.ClientSession, delivery_address: Dict[str, Any]) -> Dict[str, Any]:
    """
    Alternative approach to force delivery address change using different API endpoints.

    This tries additional methods that might be needed to actually change the delivery location:
    1. Try updating user profile delivery address
    2. Try setting location context in session
    3. Try clearing and resetting location cache

    Args:
        session: Authenticated aiohttp session
        delivery_address: Address dictionary from group order

    Returns:
        Dictionary with success status and any error messages
    """
    try:
        logger.info("🔧 Trying alternative address setting methods...")

        # Method 1: Try to update the user's default delivery address
        # This might be needed to actually persist the location change

        # Build a more complete address payload
        address_payload = {
            "address": {
                "address1": delivery_address.get('address1', ''),
                "address2": delivery_address.get('address2', ''),
                "city": delivery_address.get('city', ''),
                "state": delivery_address.get('state', ''),
                "zipcode": delivery_address.get('zipcode', ''),
                "country": delivery_address.get('country', 'US')
            },
            "latitude": delivery_address.get('latitude', 0.0),
            "longitude": delivery_address.get('longitude', 0.0),
            "isDefault": True,  # Try to make this the default
            "label": "Temporary - Group Order Location"
        }

        # Try different API endpoints that might actually persist the address change
        endpoints_to_try = [
            ("updateDeliveryAddressV1", address_payload),
            ("setDefaultDeliveryLocationV1", address_payload),
            ("updateUserLocationV1", address_payload)
        ]

        for endpoint, payload in endpoints_to_try:
            logger.info(f"🔧 Trying {endpoint}...")

            result = await make_api_request(session, endpoint, payload, use_personal_headers=True)

            if result.get('success'):
                logger.info(f"✅ {endpoint} succeeded")
                return {'success': True, 'method': endpoint}
            else:
                logger.debug(f"❌ {endpoint} failed: {result.get('error')}")

        # Method 2: Try to clear location cache and force refresh
        logger.info("🔧 Trying to clear location cache...")

        cache_clear_result = await make_api_request(session, "clearLocationCacheV1", {}, use_personal_headers=True)

        if cache_clear_result.get('success'):
            logger.info("✅ Location cache cleared")

            # Now try to set target location again
            search_query = f"{delivery_address.get('address1', '')}, {delivery_address.get('city', '')}, {delivery_address.get('state', '')}"
            retry_result = await _set_target_location_from_address(session, delivery_address)

            if retry_result.get('success'):
                logger.info("✅ Target location set successfully after cache clear")
                return {'success': True, 'method': 'cache_clear_retry'}

        # Method 3: Try to force location context in session headers
        logger.info("🔧 Trying to set location context in session...")

        # Add location headers to the session for subsequent requests
        location_headers = {
            'x-uber-location-latitude': str(delivery_address.get('latitude', 0.0)),
            'x-uber-location-longitude': str(delivery_address.get('longitude', 0.0)),
            'x-uber-delivery-address': f"{delivery_address.get('address1', '')}, {delivery_address.get('city', '')}, {delivery_address.get('state', '')}"
        }

        # Update session headers
        session.headers.update(location_headers)

        logger.info("✅ Location context headers added to session")
        return {'success': True, 'method': 'session_headers'}

    except Exception as e:
        logger.error(f"Error in alternative address setting: {str(e)}")
        return {'success': False, 'error': str(e)}


async def _set_account_delivery_address(session: aiohttp.ClientSession, delivery_address: Dict[str, Any]) -> Dict[str, Any]:
    """
    Set the delivery address in the user's account state before creating a cart.

    This approach sets the address at the account level so that when createDraftOrderV2
    is called, it uses the correct delivery address from the account state.

    Args:
        session: Authenticated aiohttp session
        delivery_address: Address dictionary from group order

    Returns:
        Dictionary with success status and any error messages
    """
    try:
        logger.info("🔧 Setting delivery address in account state...")

        # Method 1: Use the raw delivery address structure if available
        if 'raw_delivery_address' in delivery_address:
            raw_address = delivery_address['raw_delivery_address']

            # Use the EXACT payload structure from your XHR flow
            upsert_payload = {
                "isTargetLocation": True,
                "analytics": [{
                    "dataSourceType": "PLACE_DETAILS",
                    "dataSourceEndpoint": "",
                    "dataSourceImpressionID": "",
                    "cached": False
                }],
                "deliveryInstruction": {
                    "interactionType": "door_to_door",
                    "displayString": "",
                    "deliveryNotes": ""
                },
                "deliveryPayloadType": "USER_INPUT",
                "selectedInteractionType": "door_to_door",
                "referenceInfo": {
                    "placeID": raw_address.get('reference', ''),
                    "provider": raw_address.get('referenceType', 'here_places')
                },
                "addressInfo": {
                    "RESIDENCE_TYPE": "OTHER"
                },
                "label": ""
            }

            upsert_result = await make_api_request(session, "upsertDeliveryLocationV2", upsert_payload, use_personal_headers=True)

            if upsert_result.get('success'):
                logger.info("✅ Successfully upserted delivery location with raw address structure")

                # Set as target location
                target_result = await make_api_request(session, "setTargetLocationV1", {}, use_personal_headers=True)

                if target_result.get('success'):
                    logger.info("✅ Successfully set as target location")
                    return {'success': True, 'method': 'raw_address_upsert'}
                else:
                    logger.warning(f"⚠️  Failed to set target location: {target_result.get('error')}")
            else:
                logger.warning(f"⚠️  Failed to upsert raw address: {upsert_result.get('error')}")

        # Method 2: Try to add the address as a new saved location
        logger.info("🔧 Trying to add address as saved location...")

        add_location_payload = {
            "address": {
                "address1": delivery_address.get('address1', ''),
                "address2": delivery_address.get('address2', ''),
                "city": delivery_address.get('city', ''),
                "state": delivery_address.get('state', ''),
                "zipcode": delivery_address.get('zipcode', ''),
                "country": delivery_address.get('country', 'US')
            },
            "latitude": delivery_address.get('latitude', 0.0),
            "longitude": delivery_address.get('longitude', 0.0),
            "label": "Temporary - Group Order Location",
            "isDefault": True,
            "type": "OTHER"
        }

        add_result = await make_api_request(session, "addSavedLocationV1", add_location_payload, use_personal_headers=True)

        if add_result.get('success'):
            logger.info("✅ Successfully added address as saved location")

            # Try to set it as the current delivery location
            set_current_result = await make_api_request(session, "setCurrentDeliveryLocationV1", add_location_payload, use_personal_headers=True)

            if set_current_result.get('success'):
                logger.info("✅ Successfully set as current delivery location")
                return {'success': True, 'method': 'saved_location'}
            else:
                logger.warning(f"⚠️  Failed to set as current location: {set_current_result.get('error')}")
        else:
            logger.warning(f"⚠️  Failed to add saved location: {add_result.get('error')}")

        # Method 3: Try to update the user's default delivery address
        logger.info("🔧 Trying to update default delivery address...")

        update_default_payload = {
            "deliveryAddress": {
                "address": {
                    "address1": delivery_address.get('address1', ''),
                    "address2": delivery_address.get('address2', ''),
                    "city": delivery_address.get('city', ''),
                    "state": delivery_address.get('state', ''),
                    "zipcode": delivery_address.get('zipcode', ''),
                    "country": delivery_address.get('country', 'US')
                },
                "latitude": delivery_address.get('latitude', 0.0),
                "longitude": delivery_address.get('longitude', 0.0)
            }
        }

        update_result = await make_api_request(session, "updateDefaultDeliveryAddressV1", update_default_payload, use_personal_headers=True)

        if update_result.get('success'):
            logger.info("✅ Successfully updated default delivery address")
            return {'success': True, 'method': 'update_default'}
        else:
            logger.warning(f"⚠️  Failed to update default address: {update_result.get('error')}")

        return {'success': False, 'error': 'All account address setting methods failed'}

    except Exception as e:
        logger.error(f"Error setting account delivery address: {str(e)}")
        return {'success': False, 'error': str(e)}


async def _verify_account_delivery_address(session: aiohttp.ClientSession, expected_address: Dict[str, Any]) -> Dict[str, Any]:
    """
    Verify that the account's delivery address was set correctly.

    Args:
        session: Authenticated aiohttp session
        expected_address: The address we expect to be set

    Returns:
        Dictionary with success status and verification details
    """
    try:
        # Try to get the current user profile or delivery locations
        profile_result = await make_api_request(session, "getUserProfileV1", {}, use_personal_headers=True)

        if profile_result.get('success'):
            profile_data = profile_result.get('data', {})

            # Look for delivery address in profile
            if 'deliveryAddress' in profile_data:
                current_address = profile_data['deliveryAddress']
                expected_title = expected_address.get('formatted_address', expected_address.get('address1', ''))

                if expected_title.lower() in str(current_address).lower():
                    logger.info(f"✅ Profile shows expected address: {expected_title}")
                    return {'success': True, 'source': 'user_profile'}

        # Try to get current delivery locations
        locations_result = await make_api_request(session, "getDeliveryLocationsV2", {"locationTypes": ["SUGGESTED"]}, use_personal_headers=True)

        if locations_result.get('success'):
            locations_data = locations_result.get('data', {})

            if isinstance(locations_data, dict) and 'data' in locations_data:
                locations_list = locations_data['data']
            else:
                locations_list = locations_data

            if isinstance(locations_list, list):
                expected_title = expected_address.get('formatted_address', expected_address.get('address1', ''))

                for location in locations_list:
                    if isinstance(location, dict):
                        location_str = str(location).lower()
                        if expected_title.lower() in location_str:
                            logger.info(f"✅ Found expected address in delivery locations")
                            return {'success': True, 'source': 'delivery_locations'}

        return {'success': False, 'error': 'Expected address not found in account state'}

    except Exception as e:
        logger.error(f"Error verifying account delivery address: {str(e)}")
        return {'success': False, 'error': str(e)}


async def _link_address_to_draft_order(session: aiohttp.ClientSession, delivery_address: Dict[str, Any], draft_order_uuid: str) -> Dict[str, Any]:
    """
    Link the delivery address to a specific draft order.

    This is the CRITICAL missing piece - the address change must be linked to the draft order,
    not just set at the account level. Based on the XHR flow, this requires calling
    getInstructionForLocationV1 with the draftOrderUUID parameter.

    Args:
        session: Authenticated aiohttp session
        delivery_address: Address dictionary from group order
        draft_order_uuid: The draft order UUID to link the address to

    Returns:
        Dictionary with success status and any error messages
    """
    try:
        logger.info(f"🔧 Linking address to draft order: {draft_order_uuid}")

        # Step 1: First set the target location at account level (as before)
        account_result = await _set_target_location_from_address(session, delivery_address)

        if not account_result.get('success'):
            logger.warning(f"⚠️  Account-level address setting failed: {account_result.get('error')}")
            # Continue anyway - the draft order link might still work
        else:
            logger.info("✅ Account-level address set successfully")

        # Step 2: CRITICAL - Link the address to the specific draft order
        # This is the missing piece from the XHR flow!

        # Use the raw delivery address structure if available
        if 'raw_delivery_address' in delivery_address:
            raw_address = delivery_address['raw_delivery_address']
            address_data = raw_address.get('address', {})
        else:
            # Build the address structure
            address_data = {
                "address1": delivery_address.get('address1', ''),
                "address2": delivery_address.get('subtitle', ''),
                "aptOrSuite": "",
                "eaterFormattedAddress": delivery_address.get('subtitle', ''),
                "subtitle": delivery_address.get('subtitle', ''),
                "title": delivery_address.get('address1', ''),
                "uuid": ""
            }

        # Build the complete location payload with draft order UUID
        location_payload = {
            "location": {
                "address": address_data,
                "latitude": delivery_address.get('latitude', 0.0),
                "longitude": delivery_address.get('longitude', 0.0),
                "reference": delivery_address.get('reference', ''),
                "referenceType": delivery_address.get('reference_type', 'here_places'),
                "type": delivery_address.get('reference_type', 'here_places'),
                "addressComponents": {
                    "city": delivery_address.get('city', ''),
                    "countryCode": delivery_address.get('country', 'US'),
                    "firstLevelSubdivisionCode": delivery_address.get('state', ''),
                    "postalCode": delivery_address.get('zipcode', '')
                },
                "categories": ["place"],
                "originType": "user_autocomplete"
            },
            # CRITICAL: Include the draft order UUID to link the address to the cart
            "draftOrderUUID": draft_order_uuid
        }

        logger.info(f"🔗 Calling getInstructionForLocationV1 with draftOrderUUID: {draft_order_uuid}")

        # Make the critical API call that links the address to the draft order
        link_result = await make_api_request(session, "getInstructionForLocationV1", location_payload, use_personal_headers=True)

        if not link_result.get('success'):
            logger.error(f"❌ Failed to link address to draft order: {link_result.get('error')}")
            return {'success': False, 'error': f'Draft order address link failed: {link_result.get("error")}'}

        logger.info("✅ Successfully linked address to draft order")

        # CRITICAL: Refresh the cart state after address linking (from XHR flow)
        logger.info("🔄 Refreshing cart state after address linking...")

        # Step 1: Refresh checkout presentation to update cart UI
        checkout_refresh_payload = {
            "payloadTypes": [
                "cartItems", "subtotal", "basketSize", "promotion", "restrictedItems",
                "venueSectionPicker", "locationInfo", "upsellCatalogSections",
                "subTotalFareBreakdown", "canonicalProductStorePickerPayload",
                "storeSwitcherActionableBannerPayload", "fareBreakdown",
                "promoAndMembershipSavingBannerPayload", "passBanner",
                "passBannerOnCartPayload", "merchantMembership", "total",
                "paymentProfilesEligibility"
            ],
            "draftOrderUUID": draft_order_uuid,
            "isGroupOrder": False,
            "clientFeaturesData": {
                "paymentSelectionContext": {
                    "value": "{\"deviceContext\":{\"thirdPartyApplications\":[\"google_pay\"]}}"
                }
            }
        }

        refresh_result = await make_api_request(session, "getCheckoutPresentationV1", checkout_refresh_payload, use_personal_headers=True)

        if refresh_result.get('success'):
            logger.info("✅ Successfully refreshed checkout presentation")
        else:
            logger.warning(f"⚠️  Failed to refresh checkout presentation: {refresh_result.get('error')}")

        # Step 2: Refresh draft order state
        draft_refresh_payload = {"draftOrderUuid": draft_order_uuid}
        draft_refresh_result = await make_api_request(session, "getDraftOrderByUuidV1", draft_refresh_payload, use_personal_headers=True)

        if draft_refresh_result.get('success'):
            logger.info("✅ Successfully refreshed draft order state")
        else:
            logger.warning(f"⚠️  Failed to refresh draft order state: {draft_refresh_result.get('error')}")

        # Step 3: Additional checkout refresh (as seen in XHR flow)
        final_refresh_payload = {
            "payloadTypes": ["canonicalProductStorePickerPayload", "total", "subtotal", "paymentProfilesEligibility"],
            "draftOrderUUID": draft_order_uuid,
            "isGroupOrder": False,
            "clientFeaturesData": {
                "paymentSelectionContext": {
                    "value": "{\"deviceContext\":{\"thirdPartyApplications\":[\"google_pay\"]}}"
                }
            }
        }

        final_refresh_result = await make_api_request(session, "getCheckoutPresentationV1", final_refresh_payload, use_personal_headers=True)

        if final_refresh_result.get('success'):
            logger.info("✅ Successfully completed final cart refresh")
        else:
            logger.warning(f"⚠️  Failed to complete final cart refresh: {final_refresh_result.get('error')}")

        logger.info("🎯 Cart refresh sequence completed - UI should now show correct address")

        return {'success': True, 'method': 'draft_order_link_with_refresh'}

    except Exception as e:
        logger.error(f"Error linking address to draft order: {str(e)}")
        return {'success': False, 'error': str(e)}


if __name__ == "__main__":
    # Run example
    asyncio.run(main())
