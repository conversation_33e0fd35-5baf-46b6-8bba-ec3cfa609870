import asyncio
import discord
from discord.ext import commands
from discord import app_commands
import requests
import json
import re
import aiohttp
import logging

async def extract_label_data(interaction, orderlink, channel_id=None):
    """Extracts label data from an Uber Eats order and sends it to a Discord channel."""
    logging.info(f"📌 Extracting label data for link: {orderlink}")
    
    try:
        order_id_match = re.search(r"orders/([a-f0-9-]+)", orderlink)
        if not order_id_match:
            await interaction.followup.send("❌ Invalid Uber Eats order link format.", ephemeral=True)
            return
        
        order_id = order_id_match.group(1)
        url = "https://www.ubereats.com/_p/api/getActiveOrdersV1"
        
        # Headers for the API request
        headers = {
            "accept": "*/*",
            "content-type": "application/json",
            "x-csrf-token": "x"
        }
        
        payload = {
            "orderUuid": order_id,
            "timezone": "America/New_York"
        }
        
        max_retries = 3
        retry_delay = 5  # seconds
        
        for attempt in range(max_retries):
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(url, headers=headers, json=payload, timeout=30) as response:
                        response_text = await response.text()
                        if response.status == 200:
                            return await process_response(response_text, interaction, channel_id)
                        
            except asyncio.TimeoutError:
                logging.warning(f"Timeout on attempt {attempt + 1}/{max_retries}. Retrying in {retry_delay} seconds...")
                if attempt < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    continue
                else:
                    logging.error("❌ Max retries reached. Unable to connect to UberEats API")
                    await interaction.followup.send(
                        "❌ Connection timeout. Please try again in a few minutes.",
                        ephemeral=True
                    )
                    return
                    
    except Exception as e:
        logging.error(f"Error extracting label data: {str(e)}")
        await interaction.followup.send("❌ An error occurred while extracting the label data.", ephemeral=True)

async def process_response(response_text, interaction, channel_id):
    try:
        data = json.loads(response_text)
        # Log the full API response for debugging
        logging.info(f"API Response Data: {json.dumps(data, indent=2)}")
        
        # Extract the label data
        label_data = None
        orders = data.get("data", {}).get("orders", [])
        
        for item in orders:
            if "label_data" in item:
                label_data = item["label_data"]
                break
        
        if not label_data:
            await interaction.followup.send("❌ Could not find the required label data in the response.", ephemeral=True)
            return
        
        # Format the label data as a code block
        formatted_data = f"```json\n{json.dumps(label_data, indent=2)}\n```"
        
        # Determine which channel to send to
        target_channel = None
        if channel_id:
            try:
                target_channel = interaction.guild.get_channel(int(channel_id))
            except ValueError:
                await interaction.followup.send("❌ Invalid channel ID format.", ephemeral=True)
                return
        
        if not target_channel:
            target_channel = interaction.channel
        
        # Send the data to the channel
        await target_channel.send(formatted_data)
        
        # Notify the user
        await interaction.followup.send(f"✅ Label data extracted and sent to <#{target_channel.id}>", ephemeral=True)
    
    except json.JSONDecodeError:
        logging.error(f"Failed to parse JSON response: {response_text[:200]}")
        await interaction.followup.send("❌ Failed to parse the API response.", ephemeral=True)
    except Exception as e:
        logging.error(f"Error processing response: {str(e)}")
        await interaction.followup.send("❌ An error occurred while processing the response.", ephemeral=True)
