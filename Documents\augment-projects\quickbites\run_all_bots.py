import logging
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('quickbitesbot.log', encoding='utf-8')
    ]
)

logger = logging.getLogger('run_quickbitesbot')

def run_quickbitesbot():
    """Run QuickBites Bot directly."""
    try:
        # Import and run quickbitesbot directly
        from quickbitesbot.quickbitesbot import run_bot
        logger.info("Starting quickbitesbot...")
        run_bot()
    except Exception as e:
        logger.error(f"Error running quickbitesbot: {e}")
        logger.error(traceback.format_exc())

if __name__ == "__main__":
    logger.info("Starting quickbitesbot...")

    try:
        run_quickbitesbot()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down bot...")
    except Exception as e:
        logger.error(f"Error running bot: {e}")
        logger.error(traceback.format_exc())

    logger.info("Bot has been shut down.")
