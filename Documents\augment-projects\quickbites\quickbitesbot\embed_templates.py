import discord
import logging
from typing import List, Dict, Any

logger = logging.getLogger('themethodbot.embeds')

def create_locked_order_embed():
    """Create a modern embed for locked/canceled orders."""
    embed = discord.Embed(
        title="<:padlock:1396231931254935614> Group Order is Locked",
        description="This group order appears to be locked or canceled.",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add spacing to the description
    embed.description += "\n\n"

    # Add steps with better formatting
    embed.add_field(
        name="🔸 Step 1",
        value="Go back to your Uber Eats Cart",
        inline=False
    )

    embed.add_field(
        name="🔹 Step 2",
        value="Press **Unlock Group Order**",
        inline=False
    )

    embed.add_field(
        name="🔸 Step 3",
        value="Resend your cart link to this channel",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057667766026441/newpadlock.gif?ex=6879f4fb&is=6878a37b&hm=5aec7df1d1aa8f5da718c6b4da2042132a4371536aa5cd7f98e6f901c359557a&=")  # Lock icon

    return embed

# Promo status functionality removed - will be handled by PriceCheckerV2

def create_order_summary_embed(result: Dict[str, Any], cart_items: List[str], pricing_analysis: Dict[str, Any] = None) -> discord.Embed:
    """Create an enhanced order summary embed with PriceCheckerV2 pricing analysis."""
    embed = discord.Embed(
        title="<:burger:1396232260784492727> Quick Eats Order Summary",
        color=discord.Color.from_rgb(87, 242, 135)  # Vibrant Green
    )

    # Add group order link if available with better formatting
    group_link = result.get('group_link', 'Not available')
    embed.description = f"**<:link:1396231996887531630> [Group Order Link]({group_link})**\n\n"

    # Location with better formatting
    location = result.get('location', {})
    location_str = f"{location.get('address', 'Unknown Address')}"
    if location.get('city'):
        location_str += f", {location.get('city')}"
    if location.get('state'):
        location_str += f", {location.get('state').upper()}"
    if location.get('zipcode'):
        location_str += f" {location.get('zipcode')}"

    embed.add_field(
        name="<:placeholder:1396231754335129620> Delivery Location",
        value=location_str,
        inline=False
    )

    # Add store URL if available
    store_url = result.get('store_url', '')
    if store_url:
        embed.add_field(
            name="<:store:1396232154870190131> Restaurant",
            value=f"[View on Uber Eats]({store_url})",
            inline=False
        )

    # Add cart items with better formatting
    if cart_items:
        # Limit to 10 items to avoid embed being too large
        items_to_show = cart_items[:10]
        remaining_items = len(cart_items) - 10 if len(cart_items) > 10 else 0

        items_str = "\n".join([f"• {item}" for item in items_to_show])
        if remaining_items > 0:
            items_str += f"\n• ... and {remaining_items} more items"

        embed.add_field(
            name=f"<:shoppingcart:1396233174735913093> Order Items ({len(cart_items)})",
            value=items_str,
            inline=False
        )

    # PriceCheckerV2 Integration - Enhanced Pricing Display
    if pricing_analysis and pricing_analysis.get('success'):
        pricing_data = pricing_analysis.get('pricing_comparison', {})

        # Individual Order Pricing Breakdown with Method Discount & Fee
        pricing_breakdown = pricing_data.get('pricing_breakdown', {})
        if pricing_breakdown:
            original_subtotal = pricing_breakdown.get('subtotal', 0)
            original_taxes = pricing_breakdown.get('taxes', 0)

            # Apply $25 QuickEats discount to subtotal
            quickeats_discount = 25.00
            discounted_subtotal = max(0.0, original_subtotal - quickeats_discount)
            actual_discount_applied = original_subtotal - discounted_subtotal

            # Add $10 QuickEats fee to taxes
            quickeats_fee = 10.00
            updated_taxes = original_taxes + quickeats_fee

            # Calculate new total
            new_total = discounted_subtotal + updated_taxes

            breakdown_text = ""

            # Show original subtotal and discount
            breakdown_text += f"**Original Subtotal:** `${original_subtotal:.2f}`\n"
            breakdown_text += f"**Quick Eats Discount:** `-${actual_discount_applied:.2f}`\n"
            breakdown_text += f"**Discounted Subtotal:** `${discounted_subtotal:.2f}`\n\n"

            # Show taxes with QuickEats fee
            breakdown_text += f"**Taxes & Other Fees:** `${original_taxes:.2f}`\n"
            breakdown_text += f"**Quick Eats Fee:** `${quickeats_fee:.2f}`\n"
            breakdown_text += f"**Total Fees:** `${updated_taxes:.2f}`\n"

            embed.add_field(
                name="💸 Pricing Breakdown",
                value=breakdown_text,
                inline=False
            )

            # No transparency warning needed - using direct Uber One account pricing
            # The API now returns actual user pricing, not pre-discount estimates

            # Show final total
            embed.add_field(
                name="💵 Your Total (BETA)",
                value=f"`${new_total:.2f}`",
                inline=False
            )

        # Update title to show BETA status
        embed.title = "<:burger:1396232260784492727> Quick Eats Order Summary (BETA)"



        # Set simple footer
        embed.set_footer(text="Quick Eats")

    elif pricing_analysis and not pricing_analysis.get('success'):
        # Show error if pricing analysis failed
        error_msg = pricing_analysis.get('error', 'Unknown error')
        embed.add_field(
            name="⚠️ Pricing Analysis Failed",
            value=f"Could not get individual pricing: {error_msg[:100]}...",
            inline=False
        )
        embed.set_footer(text="Quick Eats | Order Summary | Pricing analysis failed")

    else:
        # No pricing analysis attempted
        embed.set_footer(text="Quick Eats | Order Summary")

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057668692705442/newfood.gif?ex=6879f4fb&is=6878a37b&hm=1e6dae283bd9828646e04b0dc1bdbeb59e3718929ec073fdaaa4163d71a54776&=")  # Food/order icon

    return embed

# Order limits checking removed - will be handled by PriceCheckerV2

def create_error_embed(error_message: str) -> discord.Embed:
    """Create a modern error embed."""
    embed = discord.Embed(
        title="<:cancel:1396231869980348467> Error Processing Order",
        description=f"We encountered an error while processing your order:\n\n```{error_message}```",
        color=discord.Color.from_rgb(237, 66, 69)  # Discord Red
    )

    # Add spacing to the description
    embed.description += "\n\n"

    # Add troubleshooting steps
    embed.add_field(
        name="<:search:1396233145048629269> Troubleshooting Steps",
        value="1. Make sure your group order is unlocked\n" +
              "2. Try creating a new group order\n" +
              "3. Check that you're using a supported restaurant",
        inline=False
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057666637758634/newAlert.gif?ex=6879f4fa&is=6878a37a&hm=2b31d1b3f7e5a58e91a10b42616dd224f62f2a4c0fb92bc3a950e9349548aca5&=")  # Warning icon

    # Add footer with support info
    embed.set_footer(text="If the issue persists, please contact support")

    return embed

def create_processing_embed() -> discord.Embed:
    """Create a modern processing embed."""
    embed = discord.Embed(
        title="🔄 Processing Your Order",
        description="We're analyzing your Uber Eats group order. This may take a few seconds...",
        color=discord.Color.from_rgb(88, 101, 242)  # Discord Blurple
    )

    # Set a nice thumbnail
    embed.set_thumbnail(url="https://media.discordapp.net/attachments/978162420423999498/1361057665857486878/newloading.gif?ex=6879f4fa&is=6878a37a&hm=7b896ea6f3adf20cc2bf55b612ba4364cd2b0d941fe706babb0fb87e3a8560fd&=")  # Loading animation

    return embed


